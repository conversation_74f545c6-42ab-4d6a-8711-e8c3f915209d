<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="src" path="A6BUSINESSTEM/resource"/>
	<classpathentry kind="src" path="A6BUSINESSTEM/src"/>
	<classpathentry kind="src" path="A6COMMON/resource"/>
	<classpathentry kind="src" path="A6COMMON/src"/>
	<classpathentry kind="src" path="A6Reference/resource"/>
	<classpathentry kind="src" path="A6Reference/src"/>
	<classpathentry kind="src" path="BUSBASE/common/resource"/>
	<classpathentry kind="src" path="BUSBASE/common/src"/>
	<classpathentry kind="src" path="BUSBASE/scm/resource"/>
	<classpathentry kind="src" path="BUSBASE/scm/src"/>
	<classpathentry kind="src" path="IA/account/resource"/>
	<classpathentry kind="src" path="IA/account/src"/>
	<classpathentry kind="src" path="IA/accountbill/resource"/>
	<classpathentry kind="src" path="IA/accountbill/src"/>
	<classpathentry kind="src" path="IA/initfinal/resource"/>
	<classpathentry kind="src" path="IA/initfinal/src"/>
	<classpathentry kind="src" path="IA/statistic/resource"/>
	<classpathentry kind="src" path="IA/statistic/src"/>
	<classpathentry kind="src" path="IA/util/resource"/>
	<classpathentry kind="src" path="IA/util/src"/>
	<classpathentry kind="src" path="OM/common/resource"/>
	<classpathentry kind="src" path="OM/common/src"/>
	<classpathentry kind="src" path="OM/invoice/resource"/>
	<classpathentry kind="src" path="OM/invoice/src"/>
	<classpathentry kind="src" path="OM/order/resource"/>
	<classpathentry kind="src" path="OM/order/src"/>
	<classpathentry kind="src" path="OM/price/resource"/>
	<classpathentry kind="src" path="OM/price/src"/>
	<classpathentry kind="src" path="OM/settle/resource"/>
	<classpathentry kind="src" path="OM/settle/src"/>
	<classpathentry kind="src" path="OM/statistic/resource"/>
	<classpathentry kind="src" path="OM/stin/resource"/>
	<classpathentry kind="src" path="OM/stin/src"/>
	<classpathentry kind="src" path="PU/init/resource"/>
	<classpathentry kind="src" path="PU/init/src"/>
	<classpathentry kind="src" path="PU/invoice/resource"/>
	<classpathentry kind="src" path="PU/invoice/src"/>
	<classpathentry kind="src" path="PU/order/resource"/>
	<classpathentry kind="src" path="PU/order/src"/>
	<classpathentry kind="src" path="PU/plan/resource"/>
	<classpathentry kind="src" path="PU/plan/src"/>
	<classpathentry kind="src" path="PU/receive/resource"/>
	<classpathentry kind="src" path="PU/receive/src"/>
	<classpathentry kind="src" path="PU/statistic/resource"/>
	<classpathentry kind="src" path="PU/statistic/src"/>
	<classpathentry kind="src" path="PU/stkin/resource"/>
	<classpathentry kind="src" path="PU/stkin/src"/>
	<classpathentry kind="src" path="PU/util/resource"/>
	<classpathentry kind="src" path="PU/util/src"/>
	<classpathentry kind="src" path="SA/account/resource"/>
	<classpathentry kind="src" path="SA/account/src"/>
	<classpathentry kind="src" path="SA/carriage/resource"/>
	<classpathentry kind="src" path="SA/carriage/src"/>
	<classpathentry kind="src" path="SA/consignsettle/resource"/>
	<classpathentry kind="src" path="SA/consignsettle/src"/>
	<classpathentry kind="src" path="SA/dispatch/resource"/>
	<classpathentry kind="src" path="SA/dispatch/src"/>
	<classpathentry kind="src" path="SA/expense/resource"/>
	<classpathentry kind="src" path="SA/expense/src"/>
	<classpathentry kind="src" path="SA/gx/resource"/>
	<classpathentry kind="src" path="SA/gx/src"/>
	<classpathentry kind="src" path="SA/invoice/resource"/>
	<classpathentry kind="src" path="SA/invoice/src"/>
	<classpathentry kind="src" path="SA/order/resource"/>
	<classpathentry kind="src" path="SA/order/src"/>
	<classpathentry kind="src" path="SA/rebate/resource"/>
	<classpathentry kind="src" path="SA/rebate/src"/>
	<classpathentry kind="src" path="SA/retail/resource"/>
	<classpathentry kind="src" path="SA/retail/src"/>
	<classpathentry kind="src" path="SA/sale_invGoldTax/resource"/>
	<classpathentry kind="src" path="SA/sale_invGoldTax/src"/>
	<classpathentry kind="src" path="SA/saprice/resource"/>
	<classpathentry kind="src" path="SA/saprice/src"/>
	<classpathentry kind="src" path="SA/statistic/resource"/>
	<classpathentry kind="src" path="SA/statistic/src"/>
	<classpathentry kind="src" path="SA/stkout/resource"/>
	<classpathentry kind="src" path="SA/stkout/src"/>
	<classpathentry kind="src" path="SA/util/resource"/>
	<classpathentry kind="src" path="SA/util/src"/>
	<classpathentry kind="src" path="SA/znj/resource"/>
	<classpathentry kind="src" path="SA/znj/src"/>
	<classpathentry kind="src" path="ST/account/resource"/>
	<classpathentry kind="src" path="ST/account/src"/>
	<classpathentry kind="src" path="ST/adjustmentbill/resource"/>
	<classpathentry kind="src" path="ST/adjustmentbill/src"/>
	<classpathentry kind="src" path="ST/assembly/resource"/>
	<classpathentry kind="src" path="ST/assembly/src"/>
	<classpathentry kind="src" path="ST/barcode/resource"/>
	<classpathentry kind="src" path="ST/barcode/src"/>
	<classpathentry kind="src" path="ST/init/resource"/>
	<classpathentry kind="src" path="ST/init/src"/>
	<classpathentry kind="src" path="ST/shapechange/resource"/>
	<classpathentry kind="src" path="ST/shapechange/src"/>
	<classpathentry kind="src" path="ST/statistic/resource"/>
	<classpathentry kind="src" path="ST/statistic/src"/>
	<classpathentry kind="src" path="ST/storagebill/resource"/>
	<classpathentry kind="src" path="ST/storagebill/src"/>
	<classpathentry kind="src" path="ST/util/resource"/>
	<classpathentry kind="src" path="ST/util/src"/>
	<classpathentry kind="src" path="UTIL/api/resource"/>
	<classpathentry kind="src" path="UTIL/api/src"/>
	<classpathentry kind="src" path="UTIL/import/resource"/>
	<classpathentry kind="src" path="UTIL/import/src"/>
	<classpathentry kind="src" path="UTIL/sysinit/resource"/>
	<classpathentry kind="src" path="UTIL/sysinit/src"/>
	<classpathentry kind="src" path="YFK/resource"/>
	<classpathentry kind="src" path="YFK/src"/>
	<classpathentry kind="src" path="accountdbtrans/resource"/>
	<classpathentry kind="src" path="accountdbtrans/src"/>
	<classpathentry kind="src" path="fastaccount/resource"/>
	<classpathentry kind="src" path="fastaccount/src"/>
	<classpathentry kind="src" path="resource"/>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="src" path="tax/invoice/resource"/>
	<classpathentry kind="src" path="tax/invoice/src"/>
	<classpathentry kind="src" path="HTTP_API/resource"/>
	<classpathentry kind="src" path="HTTP_API/src"/>
	<classpathentry exported="true" kind="con" path="org.eclipse.jdt.USER_LIBRARY/Tomcat 8.5.39"/>
	<classpathentry exported="true" kind="con" path="org.eclipse.jdt.USER_LIBRARY/lib"/>
	<classpathentry kind="output" path="build/classes"/>
</classpath>