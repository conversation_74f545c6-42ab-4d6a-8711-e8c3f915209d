# A6 HTTP API 模块

## 概述

A6 HTTP API 模块为A6企业管理系统提供RESTful HTTP接口服务，支持JSON格式的数据交换。

## 功能特性

- **统一响应格式**: 所有API返回统一的JSON响应格式
- **异常处理**: 完善的异常处理机制和错误码定义
- **跨域支持**: 支持CORS跨域请求
- **请求日志**: 详细的请求日志记录
- **健康检查**: 提供系统健康状态检查接口
- **模块化设计**: 按功能模块组织API接口
- **事务管理**: 自动事务提交和错误回滚机制
- **数据库集成**: 与A6平台数据库无缝集成

## 项目结构

```
HTTP_API/
├── src/
│   └── com/aisino/a6/http/
│       ├── api/                    # API控制器
│       │   ├── BaseResponse.java   # 统一响应格式
│       │   ├── ApiException.java   # API异常类
│       │   ├── HttpApiController.java    # 主控制器
│       │   ├── UserApiController.java    # 用户API
│       │   └── SystemApiController.java  # 系统API
│       ├── annotation/             # 注解
│       │   └── Transactional.java # 事务注解
│       └── util/                   # 工具类
│           ├── JsonUtils.java      # JSON工具
│           ├── HttpUtils.java      # HTTP工具
│           └── TransactionManager.java # 事务管理器
├── resource/
│   ├── api.properties             # API配置文件
│   └── spring/
│       └── http-api-context.xml   # Spring配置
└── README.md                      # 文档
```

## API接口文档

### 基础信息

- **Base URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

### 统一响应格式

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": 1691234567890,
    "requestId": "abc123def456"
}
```

**响应字段说明**:
- `code`: 状态码 (200=成功, 400=参数错误, 401=认证失败, 403=权限不足, 404=资源不存在, 500=服务器错误)
- `message`: 响应消息
- `data`: 响应数据
- `timestamp`: 时间戳
- `requestId`: 请求ID

### 健康检查接口

#### GET /api/health

检查系统健康状态

**响应示例**:
```json
{
    "code": 200,
    "message": "系统运行正常",
    "data": {
        "status": "UP",
        "timestamp": 1691234567890,
        "version": "1.0",
        "application": "A6 HTTP API"
    }
}
```

### 系统信息接口

#### GET /api/system/info

获取系统信息

**响应示例**:
```json
{
    "code": 200,
    "message": "获取系统信息成功",
    "data": {
        "applicationName": "A6 Enterprise System",
        "version": "7.1",
        "buildTime": "2024-08-01",
        "environment": "development",
        "jvm": {
            "javaVersion": "1.7.0_79",
            "maxMemory": "1.0 GB",
            "usedMemory": "256.0 MB"
        },
        "os": {
            "osName": "Windows 10",
            "osVersion": "10.0"
        }
    }
}
```

#### GET /api/system/time

获取系统时间

#### GET /api/system/config

获取系统配置信息

#### GET /api/system/status

获取HTTP API模块状态

**响应示例**:
```json
{
    "code": 200,
    "message": "获取API模块状态成功",
    "data": {
        "initializationStatus": "HTTP API Module Status:\n- Database Connection: OK\n...",
        "databaseConnected": true,
        "databaseInfo": "Database: Oracle, URL: ***********************************, AutoCommit: true",
        "configuration": {
            "useA6Platform": "true",
            "defaultDataSource": "default",
            "cacheEnabled": "true"
        }
    }
}
```

#### GET /api/system/db-test

测试数据库连接

**响应示例**:
```json
{
    "code": 200,
    "message": "数据库连接测试完成",
    "data": {
        "connected": true,
        "connectionInfo": "Database: Oracle, URL: ***********************************, AutoCommit: true",
        "testTime": "Thu Aug 01 10:30:00 CST 2024"
    }
}
```

### 用户管理接口

#### GET /api/user/info

获取用户信息

**参数**:
- `userId` (必填): 用户ID

**响应示例**:
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "userId": "U001",
        "userName": "用户001",
        "email": "<EMAIL>",
        "phone": "138****001",
        "status": "active",
        "createTime": "2024-01-01 10:00:00"
    }
}
```

#### GET /api/user/list

获取用户列表

**参数**:
- `page` (可选): 页码，默认1
- `size` (可选): 每页大小，默认10，最大100
- `keyword` (可选): 搜索关键字

**响应示例**:
```json
{
    "code": 200,
    "message": "获取用户列表成功",
    "data": {
        "list": [...],
        "page": 1,
        "size": 10,
        "total": 1000,
        "totalPages": 100
    }
}
```

#### POST /api/user

创建用户（支持事务）

**请求体**:
```json
{
    "userName": "新用户",
    "email": "<EMAIL>",
    "phone": "13800138000"
}
```

#### POST /api/user/batch

批量创建用户（支持事务回滚）

**请求体**:
```json
[
    {
        "userName": "用户1",
        "email": "<EMAIL>",
        "phone": "13800138001"
    },
    {
        "userName": "用户2",
        "email": "<EMAIL>",
        "phone": "13800138002"
    }
]
```

#### DELETE /api/user/{userId}

删除用户（支持事务）

#### PUT /api/user/{userId}

更新用户信息（支持事务）

## 配置说明

### api.properties 配置文件

主要配置项：
- `api.version`: API版本
- `request.enable-cors`: 是否启用CORS
- `security.enable-auth`: 是否启用认证
- `logging.enable-request-log`: 是否记录请求日志

### Spring配置

HTTP API模块的Spring配置位于 `resource/spring/http-api-context.xml`

## 部署说明

1. **编译模块**: 运行 `ant` 命令编译HTTP_API模块
2. **部署JAR**: 将生成的 `Aisino-A6-HTTP_API-7.1.jar` 部署到 `webapp/WEB-INF/lib/` 目录
3. **配置Servlet**: 确保 `web.xml` 中已配置HTTP API Servlet和初始化监听器
4. **数据库配置**: 根据需要配置数据库连接参数
5. **启动服务**: 重启应用服务器

### 数据库连接配置

HTTP API模块支持多种数据库连接方式：

#### 方式1: 使用A6平台连接（推荐）
```properties
# 系统属性配置
http.api.use.a6.platform=true
http.api.default.datasource=default
```

#### 方式2: 使用直接连接（备用）
```properties
# 系统属性配置
http.api.use.a6.platform=false
http.api.backup.driver=oracle.jdbc.driver.OracleDriver
http.api.backup.url=***********************************
http.api.backup.username=a6user
http.api.backup.password=a6pass
```

#### 方式3: 在启动参数中配置
```bash
-Dhttp.api.use.a6.platform=true
-Dhttp.api.default.datasource=default
-Dhttp.api.backup.url=****************************************
```

## 测试示例

### 使用curl测试

```bash
# 健康检查
curl -X GET http://localhost:8080/api/health

# 获取系统信息
curl -X GET http://localhost:8080/api/system/info

# 获取用户信息
curl -X GET "http://localhost:8080/api/user/info?userId=U001"

# 创建用户
curl -X POST http://localhost:8080/api/user \
  -H "Content-Type: application/json" \
  -d '{"userName":"测试用户","email":"<EMAIL>","phone":"13800138000"}'
```

## 扩展开发

### 添加新的API接口

1. 在相应的Controller类中添加新方法
2. 在 `HttpApiController` 的路由方法中添加路由规则
3. 更新API文档

### 自定义异常处理

继承 `ApiException` 类创建自定义异常，或在现有Controller中抛出标准异常。

## 事务管理

### 事务特性

HTTP API模块集成了完整的事务管理功能：

- **自动事务管理**: POST、PUT、DELETE操作自动启用事务
- **事务提交**: 操作成功时自动提交事务
- **事务回滚**: 发生异常时自动回滚事务
- **连接管理**: 自动管理数据库连接的获取和释放

### 事务注解

使用`@Transactional`注解标记需要事务管理的方法：

```java
@Transactional
public BaseResponse<?> createUser(String requestBody) throws ApiException {
    // 业务逻辑
}
```

### 事务管理器API

```java
// 手动事务管理
TransactionManager.beginTransaction();
try {
    // 业务操作
    TransactionManager.commitTransaction();
} catch (Exception e) {
    TransactionManager.rollbackTransaction();
}

// 使用事务操作接口
TransactionManager.executeInTransaction(new TransactionOperation<String>() {
    @Override
    public String execute() throws Exception {
        // 业务逻辑
        return result;
    }
});
```

### 事务示例

#### 成功提交示例
```bash
# 创建单个用户（事务自动提交）
curl -X POST http://localhost:8080/api/user \
  -H "Content-Type: application/json" \
  -d '{"userName":"测试用户","email":"<EMAIL>","phone":"13800138000"}'
```

#### 回滚示例
```bash
# 批量创建用户，如果某个用户数据有问题，整个批次都会回滚
curl -X POST http://localhost:8080/api/user/batch \
  -H "Content-Type: application/json" \
  -d '[
    {"userName":"正常用户","email":"<EMAIL>","phone":"13800138001"},
    {"userName":"error用户","email":"invalid","phone":"13800138002"}
  ]'
```

### 事务日志

事务操作会产生详细的日志：

```
Transaction started - Connection: 12345678
User created in database: U1691234567890
Created 3 default settings for user: U1691234567890
Request completed successfully in transaction - Request ID: abc123def456
Transaction committed - Connection: 12345678
Connection closed - Connection: 12345678
```

## 注意事项

- 所有API接口都返回JSON格式数据
- 请求和响应的字符编码统一使用UTF-8
- 建议在生产环境中启用认证和授权机制
- 定期检查和清理日志文件
- 事务超时时间默认为30秒，可在配置文件中调整
- 长时间运行的操作建议拆分为多个小事务
