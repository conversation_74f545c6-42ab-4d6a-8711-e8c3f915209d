# A6 HTTP API 模块

## 概述

A6 HTTP API 模块为A6企业管理系统提供RESTful HTTP接口服务，支持JSON格式的数据交换。

## 功能特性

- **统一响应格式**: 所有API返回统一的JSON响应格式
- **异常处理**: 完善的异常处理机制和错误码定义
- **跨域支持**: 支持CORS跨域请求
- **请求日志**: 详细的请求日志记录
- **健康检查**: 提供系统健康状态检查接口
- **模块化设计**: 按功能模块组织API接口

## 项目结构

```
HTTP_API/
├── src/
│   └── com/aisino/a6/http/
│       ├── api/                    # API控制器
│       │   ├── BaseResponse.java   # 统一响应格式
│       │   ├── ApiException.java   # API异常类
│       │   ├── HttpApiController.java    # 主控制器
│       │   ├── UserApiController.java    # 用户API
│       │   └── SystemApiController.java  # 系统API
│       └── util/                   # 工具类
│           ├── JsonUtils.java      # JSON工具
│           └── HttpUtils.java      # HTTP工具
├── resource/
│   ├── api.properties             # API配置文件
│   └── spring/
│       └── http-api-context.xml   # Spring配置
└── README.md                      # 文档
```

## API接口文档

### 基础信息

- **Base URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

### 统一响应格式

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": 1691234567890,
    "requestId": "abc123def456"
}
```

**响应字段说明**:
- `code`: 状态码 (200=成功, 400=参数错误, 401=认证失败, 403=权限不足, 404=资源不存在, 500=服务器错误)
- `message`: 响应消息
- `data`: 响应数据
- `timestamp`: 时间戳
- `requestId`: 请求ID

### 健康检查接口

#### GET /api/health

检查系统健康状态

**响应示例**:
```json
{
    "code": 200,
    "message": "系统运行正常",
    "data": {
        "status": "UP",
        "timestamp": 1691234567890,
        "version": "1.0",
        "application": "A6 HTTP API"
    }
}
```

### 系统信息接口

#### GET /api/system/info

获取系统信息

**响应示例**:
```json
{
    "code": 200,
    "message": "获取系统信息成功",
    "data": {
        "applicationName": "A6 Enterprise System",
        "version": "7.1",
        "buildTime": "2024-08-01",
        "environment": "development",
        "jvm": {
            "javaVersion": "1.7.0_79",
            "maxMemory": "1.0 GB",
            "usedMemory": "256.0 MB"
        },
        "os": {
            "osName": "Windows 10",
            "osVersion": "10.0"
        }
    }
}
```

#### GET /api/system/time

获取系统时间

#### GET /api/system/config

获取系统配置信息

### 用户管理接口

#### GET /api/user/info

获取用户信息

**参数**:
- `userId` (必填): 用户ID

**响应示例**:
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "userId": "U001",
        "userName": "用户001",
        "email": "<EMAIL>",
        "phone": "138****001",
        "status": "active",
        "createTime": "2024-01-01 10:00:00"
    }
}
```

#### GET /api/user/list

获取用户列表

**参数**:
- `page` (可选): 页码，默认1
- `size` (可选): 每页大小，默认10，最大100
- `keyword` (可选): 搜索关键字

**响应示例**:
```json
{
    "code": 200,
    "message": "获取用户列表成功",
    "data": {
        "list": [...],
        "page": 1,
        "size": 10,
        "total": 1000,
        "totalPages": 100
    }
}
```

#### POST /api/user

创建用户

**请求体**:
```json
{
    "userName": "新用户",
    "email": "<EMAIL>",
    "phone": "13800138000"
}
```

## 配置说明

### api.properties 配置文件

主要配置项：
- `api.version`: API版本
- `request.enable-cors`: 是否启用CORS
- `security.enable-auth`: 是否启用认证
- `logging.enable-request-log`: 是否记录请求日志

### Spring配置

HTTP API模块的Spring配置位于 `resource/spring/http-api-context.xml`

## 部署说明

1. **编译模块**: 运行 `ant` 命令编译HTTP_API模块
2. **部署JAR**: 将生成的 `Aisino-A6-HTTP_API-7.1.jar` 部署到 `webapp/WEB-INF/lib/` 目录
3. **配置Servlet**: 确保 `web.xml` 中已配置HTTP API Servlet
4. **启动服务**: 重启应用服务器

## 测试示例

### 使用curl测试

```bash
# 健康检查
curl -X GET http://localhost:8080/api/health

# 获取系统信息
curl -X GET http://localhost:8080/api/system/info

# 获取用户信息
curl -X GET "http://localhost:8080/api/user/info?userId=U001"

# 创建用户
curl -X POST http://localhost:8080/api/user \
  -H "Content-Type: application/json" \
  -d '{"userName":"测试用户","email":"<EMAIL>","phone":"13800138000"}'
```

## 扩展开发

### 添加新的API接口

1. 在相应的Controller类中添加新方法
2. 在 `HttpApiController` 的路由方法中添加路由规则
3. 更新API文档

### 自定义异常处理

继承 `ApiException` 类创建自定义异常，或在现有Controller中抛出标准异常。

## 注意事项

- 所有API接口都返回JSON格式数据
- 请求和响应的字符编码统一使用UTF-8
- 建议在生产环境中启用认证和授权机制
- 定期检查和清理日志文件
