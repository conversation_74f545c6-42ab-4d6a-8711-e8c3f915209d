# HTTP API????
# A6 System HTTP API Configuration

# API????
api.version=1.0
api.name=A6 HTTP API
api.description=A6??????HTTP????

# ?????
server.port=8080
server.context-path=/api
server.encoding=UTF-8

# ????
request.max-size=10485760
request.timeout=30000
request.enable-cors=true

# ????
response.default-content-type=application/json
response.enable-gzip=true

# ????
security.enable-auth=false
security.enable-rate-limit=true
security.rate-limit.requests-per-hour=1000
security.enable-ip-whitelist=false
security.ip-whitelist=127.0.0.1,localhost

# ????
logging.enable-request-log=true
logging.enable-response-log=false
logging.enable-error-log=true
logging.log-level=INFO

# ????
cache.enable=true
cache.type=memory
cache.max-size=1000
cache.expire-time=3600

# ???????????
database.enable=false
database.driver=oracle.jdbc.driver.OracleDriver
database.url=***********************************
database.username=a6user
database.password=a6pass
database.max-connections=20
database.connection-timeout=30000

# ????
monitor.enable=true
monitor.enable-health-check=true
monitor.enable-metrics=true
monitor.metrics-interval=60000

# ??????
error.enable-stack-trace=false
error.enable-detail-message=true
error.default-error-code=500
error.default-error-message=??????
