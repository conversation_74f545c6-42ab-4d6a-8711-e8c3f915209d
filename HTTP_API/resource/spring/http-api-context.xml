<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context 
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop 
           http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx 
           http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

    <!-- HTTP API模块Spring配置 -->
    
    <!-- 属性文件配置 -->
    <context:property-placeholder location="classpath:api.properties" ignore-unresolvable="true"/>
    
    <!-- 组件扫描 -->
    <context:component-scan base-package="com.aisino.a6.http" />
    
    <!-- 启用AOP -->
    <aop:aspectj-autoproxy />
    
    <!-- HTTP API控制器 -->
    <bean id="httpApiController" class="com.aisino.a6.http.api.HttpApiController" />
    
    <!-- 用户API控制器 -->
    <bean id="userApiController" class="com.aisino.a6.http.api.UserApiController" />
    
    <!-- 系统API控制器 -->
    <bean id="systemApiController" class="com.aisino.a6.http.api.SystemApiController" />
    
    <!-- JSON工具类 -->
    <bean id="jsonUtils" class="com.aisino.a6.http.util.JsonUtils" />
    
    <!-- HTTP工具类 -->
    <bean id="httpUtils" class="com.aisino.a6.http.util.HttpUtils" />
    
    <!-- API配置Bean -->
    <bean id="apiConfig" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="version" value="${api.version:1.0}" />
                <entry key="name" value="${api.name:A6 HTTP API}" />
                <entry key="description" value="${api.description:A6企业管理系统HTTP接口服务}" />
                <entry key="enableCors" value="${request.enable-cors:true}" />
                <entry key="enableAuth" value="${security.enable-auth:false}" />
                <entry key="maxRequestSize" value="${request.max-size:10485760}" />
                <entry key="requestTimeout" value="${request.timeout:30000}" />
                <entry key="enableRateLimit" value="${security.enable-rate-limit:true}" />
                <entry key="rateLimitPerHour" value="${security.rate-limit.requests-per-hour:1000}" />
            </map>
        </constructor-arg>
    </bean>
    
    <!-- 缓存配置（如果启用） -->
    <bean id="cacheConfig" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="enable" value="${cache.enable:true}" />
                <entry key="type" value="${cache.type:memory}" />
                <entry key="maxSize" value="${cache.max-size:1000}" />
                <entry key="expireTime" value="${cache.expire-time:3600}" />
            </map>
        </constructor-arg>
    </bean>
    
    <!-- 监控配置 -->
    <bean id="monitorConfig" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="enable" value="${monitor.enable:true}" />
                <entry key="enableHealthCheck" value="${monitor.enable-health-check:true}" />
                <entry key="enableMetrics" value="${monitor.enable-metrics:true}" />
                <entry key="metricsInterval" value="${monitor.metrics-interval:60000}" />
            </map>
        </constructor-arg>
    </bean>
    
    <!-- 日志配置 -->
    <bean id="loggingConfig" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="enableRequestLog" value="${logging.enable-request-log:true}" />
                <entry key="enableResponseLog" value="${logging.enable-response-log:false}" />
                <entry key="enableErrorLog" value="${logging.enable-error-log:true}" />
                <entry key="logLevel" value="${logging.log-level:INFO}" />
            </map>
        </constructor-arg>
    </bean>
    
    <!-- 错误处理配置 -->
    <bean id="errorConfig" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="enableStackTrace" value="${error.enable-stack-trace:false}" />
                <entry key="enableDetailMessage" value="${error.enable-detail-message:true}" />
                <entry key="defaultErrorCode" value="${error.default-error-code:500}" />
                <entry key="defaultErrorMessage" value="${error.default-error-message:系统内部错误}" />
            </map>
        </constructor-arg>
    </bean>

</beans>
