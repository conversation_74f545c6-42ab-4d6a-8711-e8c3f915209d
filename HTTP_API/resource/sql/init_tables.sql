-- HTTP API模块数据库初始化脚本
-- 创建用户和用户设置表

-- 创建用户表
CREATE TABLE users (
    user_id VARCHAR2(50) PRIMARY KEY,
    user_name VARCHAR2(100) NOT NULL,
    email VARCHAR2(200) NOT NULL UNIQUE,
    phone VARCHAR2(20),
    status VARCHAR2(20) DEFAULT 'active',
    create_time DATE DEFAULT SYSDATE,
    update_time DATE DEFAULT SYSDATE
);

-- 创建用户设置表
CREATE TABLE user_settings (
    setting_id VARCHAR2(50) PRIMARY KEY,
    user_id VARCHAR2(50) NOT NULL,
    setting_key VARCHAR2(100) NOT NULL,
    setting_value VARCHAR2(500),
    create_time DATE DEFAULT SYSDATE,
    update_time DATE DEFAULT SYSDATE,
    CONSTRAINT fk_user_settings_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_user_settings_key ON user_settings(setting_key);

-- 创建序列（用于生成设置ID）
CREATE SEQUENCE seq_user_settings_id START WITH 1 INCREMENT BY 1;

-- 创建触发器（自动生成设置ID）
CREATE OR REPLACE TRIGGER trg_user_settings_id
    BEFORE INSERT ON user_settings
    FOR EACH ROW
BEGIN
    IF :NEW.setting_id IS NULL THEN
        SELECT 'SET' || LPAD(seq_user_settings_id.NEXTVAL, 10, '0') INTO :NEW.setting_id FROM DUAL;
    END IF;
END;
/

-- 创建触发器（自动更新时间）
CREATE OR REPLACE TRIGGER trg_users_update_time
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    :NEW.update_time := SYSDATE;
END;
/

CREATE OR REPLACE TRIGGER trg_user_settings_update_time
    BEFORE UPDATE ON user_settings
    FOR EACH ROW
BEGIN
    :NEW.update_time := SYSDATE;
END;
/

-- 插入测试数据
INSERT INTO users (user_id, user_name, email, phone, status) VALUES 
('U001', '管理员', '<EMAIL>', '13800138000', 'active');

INSERT INTO users (user_id, user_name, email, phone, status) VALUES 
('U002', '测试用户', '<EMAIL>', '13800138001', 'active');

-- 插入默认设置
INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES 
('U001', 'language', 'zh_CN');
INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES 
('U001', 'theme', 'default');
INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES 
('U001', 'timezone', 'Asia/Shanghai');

INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES 
('U002', 'language', 'zh_CN');
INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES 
('U002', 'theme', 'dark');
INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES 
('U002', 'timezone', 'Asia/Shanghai');

-- 提交事务
COMMIT;

-- 验证数据
SELECT 'Users Count: ' || COUNT(*) FROM users;
SELECT 'Settings Count: ' || COUNT(*) FROM user_settings;

-- 显示创建的表
SELECT table_name FROM user_tables WHERE table_name IN ('USERS', 'USER_SETTINGS');

PROMPT 'HTTP API数据库初始化完成！';
