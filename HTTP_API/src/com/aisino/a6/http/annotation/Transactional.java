package com.aisino.a6.http.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 事务注解
 * 用于标记需要事务管理的方法
 * 
 * <AUTHOR> System
 * @version 1.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Transactional {
    
    /**
     * 事务传播行为
     */
    Propagation propagation() default Propagation.REQUIRED;
    
    /**
     * 事务隔离级别
     */
    Isolation isolation() default Isolation.DEFAULT;
    
    /**
     * 是否只读事务
     */
    boolean readOnly() default false;
    
    /**
     * 事务超时时间（秒）
     */
    int timeout() default -1;
    
    /**
     * 需要回滚的异常类型
     */
    Class<? extends Throwable>[] rollbackFor() default {};
    
    /**
     * 不需要回滚的异常类型
     */
    Class<? extends Throwable>[] noRollbackFor() default {};
    
    /**
     * 事务传播行为枚举
     */
    enum Propagation {
        REQUIRED,       // 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务
        REQUIRES_NEW,   // 创建一个新的事务，如果当前存在事务，则把当前事务挂起
        SUPPORTS,       // 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务的方式继续运行
        NOT_SUPPORTED,  // 以非事务方式运行，如果当前存在事务，则把当前事务挂起
        MANDATORY,      // 如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常
        NEVER          // 以非事务方式运行，如果当前存在事务，则抛出异常
    }
    
    /**
     * 事务隔离级别枚举
     */
    enum Isolation {
        DEFAULT,                // 使用数据库默认的隔离级别
        READ_UNCOMMITTED,       // 读未提交
        READ_COMMITTED,         // 读已提交
        REPEATABLE_READ,        // 可重复读
        SERIALIZABLE           // 串行化
    }
}
