package com.aisino.a6.http.api;

/**
 * API异常类
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class ApiException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    /** 错误码 */
    private int errorCode;
    
    /** 错误消息 */
    private String errorMessage;
    
    public ApiException() {
        super();
        this.errorCode = BaseResponse.ERROR_CODE;
    }
    
    public ApiException(String message) {
        super(message);
        this.errorCode = BaseResponse.ERROR_CODE;
        this.errorMessage = message;
    }
    
    public ApiException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    public ApiException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = BaseResponse.ERROR_CODE;
        this.errorMessage = message;
    }
    
    public ApiException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    public int getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    /**
     * 创建参数错误异常
     */
    public static ApiException paramError(String message) {
        return new ApiException(BaseResponse.PARAM_ERROR_CODE, message);
    }
    
    /**
     * 创建认证错误异常
     */
    public static ApiException authError(String message) {
        return new ApiException(BaseResponse.AUTH_ERROR_CODE, message);
    }
    
    /**
     * 创建权限错误异常
     */
    public static ApiException forbiddenError(String message) {
        return new ApiException(BaseResponse.FORBIDDEN_CODE, message);
    }
    
    /**
     * 创建资源未找到异常
     */
    public static ApiException notFoundError(String message) {
        return new ApiException(BaseResponse.NOT_FOUND_CODE, message);
    }
    
    @Override
    public String toString() {
        return "ApiException{" +
                "errorCode=" + errorCode +
                ", errorMessage='" + errorMessage + '\'' +
                "} " + super.toString();
    }
}
