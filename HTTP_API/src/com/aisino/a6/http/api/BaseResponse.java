package com.aisino.a6.http.api;

import java.io.Serializable;

/**
 * HTTP API统一响应格式
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class BaseResponse<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 响应状态码 */
    private int code;
    
    /** 响应消息 */
    private String message;
    
    /** 响应数据 */
    private T data;
    
    /** 时间戳 */
    private long timestamp;
    
    /** 请求ID */
    private String requestId;
    
    // 常用状态码常量
    public static final int SUCCESS_CODE = 200;
    public static final int ERROR_CODE = 500;
    public static final int PARAM_ERROR_CODE = 400;
    public static final int AUTH_ERROR_CODE = 401;
    public static final int FORBIDDEN_CODE = 403;
    public static final int NOT_FOUND_CODE = 404;
    
    public BaseResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public BaseResponse(int code, String message) {
        this();
        this.code = code;
        this.message = message;
    }
    
    public BaseResponse(int code, String message, T data) {
        this(code, message);
        this.data = data;
    }
    
    /**
     * 创建成功响应
     */
    public static <T> BaseResponse<T> success() {
        return new BaseResponse<T>(SUCCESS_CODE, "操作成功");
    }
    
    /**
     * 创建成功响应（带数据）
     */
    public static <T> BaseResponse<T> success(T data) {
        return new BaseResponse<T>(SUCCESS_CODE, "操作成功", data);
    }
    
    /**
     * 创建成功响应（自定义消息）
     */
    public static <T> BaseResponse<T> success(String message, T data) {
        return new BaseResponse<T>(SUCCESS_CODE, message, data);
    }
    
    /**
     * 创建错误响应
     */
    public static <T> BaseResponse<T> error(String message) {
        return new BaseResponse<T>(ERROR_CODE, message);
    }
    
    /**
     * 创建错误响应（自定义状态码）
     */
    public static <T> BaseResponse<T> error(int code, String message) {
        return new BaseResponse<T>(code, message);
    }
    
    /**
     * 创建参数错误响应
     */
    public static <T> BaseResponse<T> paramError(String message) {
        return new BaseResponse<T>(PARAM_ERROR_CODE, message);
    }
    
    /**
     * 创建认证错误响应
     */
    public static <T> BaseResponse<T> authError(String message) {
        return new BaseResponse<T>(AUTH_ERROR_CODE, message);
    }
    
    // Getter和Setter方法
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code == SUCCESS_CODE;
    }
    
    @Override
    public String toString() {
        return "BaseResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                ", requestId='" + requestId + '\'' +
                '}';
    }
}
