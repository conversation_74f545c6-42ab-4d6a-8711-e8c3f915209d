package com.aisino.a6.http.api;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.aisino.a6.http.util.HttpUtils;
import com.aisino.a6.http.util.JsonUtils;
import com.aisino.a6.http.util.TransactionManager;
import com.aisino.platform.core.Guid;
import com.aisino.platform.db.DbSvr;

/**
 * HTTP API主控制器
 * 处理所有HTTP API请求的入口点
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class HttpApiController extends HttpServlet {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        BillDealPlugin billDealPlugin = new BillDealPlugin();
        billDealPlugin.billinsert(request, response);
        //handleRequest(request, response);
    }
    
    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doDelete(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doOptions(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 处理预检请求
        HttpUtils.setJsonResponse(response);
        response.setStatus(HttpServletResponse.SC_OK);
    }
    
    /**
     * 统一处理所有HTTP请求
     */
    private void handleRequest(HttpServletRequest request, HttpServletResponse response) {
        String requestId = HttpUtils.generateRequestId();
        BaseResponse<?> result = null;

        try {
            // 记录请求信息
            logRequestInfo(request, requestId);

            // 获取请求路径
            String pathInfo = request.getPathInfo();
            if (pathInfo == null) {
                pathInfo = "";
            }

            // 判断是否需要事务处理
            if (needsTransaction(request, pathInfo)) {
                result = handleRequestWithTransaction(request, pathInfo, requestId);
            } else {
                result = routeRequest(request, pathInfo);
            }

            // 设置请求ID
            if (result != null) {
                result.setRequestId(requestId);
            }

        } catch (ApiException e) {
            result = BaseResponse.error(e.getErrorCode(), e.getErrorMessage());
            result.setRequestId(requestId);
        } catch (Exception e) {
            result = BaseResponse.error("系统内部错误: " + e.getMessage());
            result.setRequestId(requestId);
            e.printStackTrace();
        } finally {
            // 确保事务资源被清理
            if (TransactionManager.isTransactionActive()) {
                TransactionManager.forceCleanup();
            }
        }

        // 返回响应
        HttpUtils.writeJsonResponse(response, result);
    }
    
    /**
     * 路由请求到具体的处理方法
     */
    private BaseResponse<?> routeRequest(HttpServletRequest request, String pathInfo) throws ApiException {
        String method = request.getMethod().toUpperCase();
        
        // 根据路径和方法路由到不同的处理器
        if (pathInfo.startsWith("/user")) {
            return handleUserApi(request, pathInfo, method);
        } else if (pathInfo.startsWith("/system")) {
            return handleSystemApi(request, pathInfo, method);
        } else if (pathInfo.equals("/health") || pathInfo.equals("/")) {
            return handleHealthCheck(request);
        } else {
            throw ApiException.notFoundError("API接口不存在: " + pathInfo);
        }
    }
    
    /**
     * 处理用户相关API
     */
    private BaseResponse<?> handleUserApi(HttpServletRequest request, String pathInfo, String method)
            throws ApiException {
        UserApiController userController = new UserApiController();

        if (pathInfo.equals("/user/info") && "GET".equals(method)) {
            String userId = request.getParameter("userId");
            return userController.getUserInfo(userId);
        } else if (pathInfo.equals("/user/list") && "GET".equals(method)) {
            return userController.getUserList(request);
        } else if (pathInfo.equals("/user") && "POST".equals(method)) {
            String requestBody = HttpUtils.getRequestBody(request);
            return userController.createUser(requestBody);
        } else if (pathInfo.equals("/user/batch") && "POST".equals(method)) {
            String requestBody = HttpUtils.getRequestBody(request);
            return userController.batchCreateUsers(requestBody);
        } else if (pathInfo.startsWith("/user/") && "DELETE".equals(method)) {
            String userId = pathInfo.substring("/user/".length());
            return userController.deleteUser(userId);
        } else if (pathInfo.startsWith("/user/") && "PUT".equals(method)) {
            String userId = pathInfo.substring("/user/".length());
            String requestBody = HttpUtils.getRequestBody(request);
            return userController.updateUser(userId, requestBody);
        } else {
            throw ApiException.notFoundError("用户API接口不存在: " + pathInfo);
        }
    }
    
    /**
     * 处理系统相关API
     */
    private BaseResponse<?> handleSystemApi(HttpServletRequest request, String pathInfo, String method) 
            throws ApiException {
        SystemApiController systemController = new SystemApiController();
        
        if (pathInfo.equals("/system/info") && "GET".equals(method)) {
            return systemController.getSystemInfo();
        } else if (pathInfo.equals("/system/time") && "GET".equals(method)) {
            return systemController.getSystemTime();
        } else if (pathInfo.equals("/system/config") && "GET".equals(method)) {
            return systemController.getSystemConfig();
        } else {
            throw ApiException.notFoundError("系统API接口不存在: " + pathInfo);
        }
    }
    
    /**
     * 处理健康检查
     */
    private BaseResponse<?> handleHealthCheck(HttpServletRequest request) {
        Map<String, Object> healthInfo = new java.util.HashMap<String, Object>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", System.currentTimeMillis());
        healthInfo.put("version", "1.0");
        healthInfo.put("application", "A6 HTTP API");
        
        return BaseResponse.success("系统运行正常", healthInfo);
    }
    
    /**
     * 判断请求是否需要事务处理
     */
    private boolean needsTransaction(HttpServletRequest request, String pathInfo) {
        String method = request.getMethod().toUpperCase();

        // POST、PUT、DELETE操作通常需要事务
        if ("POST".equals(method) || "PUT".equals(method) || "DELETE".equals(method)) {
            return true;
        }

        // 特定的查询操作如果涉及复杂业务逻辑也可能需要事务
        if ("GET".equals(method)) {
            // 可以根据具体的API路径判断是否需要事务
            if (pathInfo.contains("/complex") || pathInfo.contains("/report")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 在事务中处理请求
     */
    private BaseResponse<?> handleRequestWithTransaction(HttpServletRequest request, String pathInfo, String requestId)
            throws Exception {

        return TransactionManager.executeInTransaction(new TransactionManager.TransactionOperation<BaseResponse<?>>() {
            @Override
            public BaseResponse<?> execute() throws Exception {
                System.out.println("Executing request in transaction - Request ID: " + requestId);
                System.out.println("Transaction Status: " + TransactionManager.getTransactionStatus());

                BaseResponse<?> result = routeRequest(request, pathInfo);

                // 在事务提交前可以进行额外的验证
                if (result != null && !result.isSuccess()) {
                    throw new ApiException(result.getCode(), result.getMessage());
                }

                System.out.println("Request completed successfully in transaction - Request ID: " + requestId);
                return result;
            }
        });
    }

    /**
     * 记录请求信息
     */
    private void logRequestInfo(HttpServletRequest request, String requestId) {
        String method = request.getMethod();
        String url = HttpUtils.getFullRequestUrl(request);
        String clientIp = HttpUtils.getClientIpAddress(request);

        System.out.println("=== HTTP API Request ===");
        System.out.println("Request ID: " + requestId);
        System.out.println("Method: " + method);
        System.out.println("URL: " + url);
        System.out.println("Client IP: " + clientIp);
        System.out.println("User-Agent: " + request.getHeader("User-Agent"));
        System.out.println("Transaction Required: " + needsTransaction(request, request.getPathInfo()));
        System.out.println("========================");
    }
}
