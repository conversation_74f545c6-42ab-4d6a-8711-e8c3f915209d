package com.aisino.a6.http.api;

import java.io.IOException;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.aisino.a6.http.util.HttpUtils;
import com.aisino.a6.http.util.JsonUtils;

/**
 * HTTP API主控制器
 * 处理所有HTTP API请求的入口点
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class HttpApiController extends HttpServlet {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doDelete(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doOptions(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 处理预检请求
        HttpUtils.setJsonResponse(response);
        response.setStatus(HttpServletResponse.SC_OK);
    }
    
    /**
     * 统一处理所有HTTP请求
     */
    private void handleRequest(HttpServletRequest request, HttpServletResponse response) {
        String requestId = HttpUtils.generateRequestId();
        BaseResponse<?> result = null;
        
        try {
            // 记录请求信息
            logRequestInfo(request, requestId);
            
            // 获取请求路径
            String pathInfo = request.getPathInfo();
            if (pathInfo == null) {
                pathInfo = "";
            }
            
            // 路由到具体的处理方法
            result = routeRequest(request, pathInfo);
            
            // 设置请求ID
            if (result != null) {
                result.setRequestId(requestId);
            }
            
        } catch (ApiException e) {
            result = BaseResponse.error(e.getErrorCode(), e.getErrorMessage());
            result.setRequestId(requestId);
        } catch (Exception e) {
            result = BaseResponse.error("系统内部错误: " + e.getMessage());
            result.setRequestId(requestId);
            e.printStackTrace();
        }
        
        // 返回响应
        HttpUtils.writeJsonResponse(response, result);
    }
    
    /**
     * 路由请求到具体的处理方法
     */
    private BaseResponse<?> routeRequest(HttpServletRequest request, String pathInfo) throws ApiException {
        String method = request.getMethod().toUpperCase();
        
        // 根据路径和方法路由到不同的处理器
        if (pathInfo.startsWith("/user")) {
            return handleUserApi(request, pathInfo, method);
        } else if (pathInfo.startsWith("/system")) {
            return handleSystemApi(request, pathInfo, method);
        } else if (pathInfo.equals("/health") || pathInfo.equals("/")) {
            return handleHealthCheck(request);
        } else {
            throw ApiException.notFoundError("API接口不存在: " + pathInfo);
        }
    }
    
    /**
     * 处理用户相关API
     */
    private BaseResponse<?> handleUserApi(HttpServletRequest request, String pathInfo, String method) 
            throws ApiException {
        UserApiController userController = new UserApiController();
        
        if (pathInfo.equals("/user/info") && "GET".equals(method)) {
            String userId = request.getParameter("userId");
            return userController.getUserInfo(userId);
        } else if (pathInfo.equals("/user/list") && "GET".equals(method)) {
            return userController.getUserList(request);
        } else if (pathInfo.equals("/user") && "POST".equals(method)) {
            String requestBody = HttpUtils.getRequestBody(request);
            return userController.createUser(requestBody);
        } else {
            throw ApiException.notFoundError("用户API接口不存在: " + pathInfo);
        }
    }
    
    /**
     * 处理系统相关API
     */
    private BaseResponse<?> handleSystemApi(HttpServletRequest request, String pathInfo, String method) 
            throws ApiException {
        SystemApiController systemController = new SystemApiController();
        
        if (pathInfo.equals("/system/info") && "GET".equals(method)) {
            return systemController.getSystemInfo();
        } else if (pathInfo.equals("/system/time") && "GET".equals(method)) {
            return systemController.getSystemTime();
        } else if (pathInfo.equals("/system/config") && "GET".equals(method)) {
            return systemController.getSystemConfig();
        } else {
            throw ApiException.notFoundError("系统API接口不存在: " + pathInfo);
        }
    }
    
    /**
     * 处理健康检查
     */
    private BaseResponse<?> handleHealthCheck(HttpServletRequest request) {
        Map<String, Object> healthInfo = new java.util.HashMap<String, Object>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", System.currentTimeMillis());
        healthInfo.put("version", "1.0");
        healthInfo.put("application", "A6 HTTP API");
        
        return BaseResponse.success("系统运行正常", healthInfo);
    }
    
    /**
     * 记录请求信息
     */
    private void logRequestInfo(HttpServletRequest request, String requestId) {
        String method = request.getMethod();
        String url = HttpUtils.getFullRequestUrl(request);
        String clientIp = HttpUtils.getClientIpAddress(request);
        
        System.out.println("=== HTTP API Request ===");
        System.out.println("Request ID: " + requestId);
        System.out.println("Method: " + method);
        System.out.println("URL: " + url);
        System.out.println("Client IP: " + clientIp);
        System.out.println("User-Agent: " + request.getHeader("User-Agent"));
        System.out.println("========================");
    }
}
