package com.aisino.a6.http.api;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.a6.http.util.HttpUtils;
import com.aisino.a6.http.util.JsonUtils;

/**
 * 用户API控制器
 * 处理用户相关的HTTP API请求
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class UserApiController {
    
    /**
     * 获取用户信息
     */
    public BaseResponse<?> getUserInfo(String userId) throws ApiException {
        if (userId == null || userId.trim().isEmpty()) {
            throw ApiException.paramError("用户ID不能为空");
        }
        
        // 模拟从数据库获取用户信息
        Map<String, Object> userInfo = new HashMap<String, Object>();
        userInfo.put("userId", userId);
        userInfo.put("userName", "用户" + userId);
        userInfo.put("email", "user" + userId + "@example.com");
        userInfo.put("phone", "138****" + userId.substring(Math.max(0, userId.length() - 4)));
        userInfo.put("status", "active");
        userInfo.put("createTime", "2024-01-01 10:00:00");
        userInfo.put("lastLoginTime", "2024-08-01 09:30:00");
        
        return BaseResponse.success("获取用户信息成功", userInfo);
    }
    
    /**
     * 获取用户列表
     */
    public BaseResponse<?> getUserList(HttpServletRequest request) throws ApiException {
        // 获取分页参数
        String pageStr = request.getParameter("page");
        String sizeStr = request.getParameter("size");
        String keyword = request.getParameter("keyword");
        
        int page = 1;
        int size = 10;
        
        try {
            if (pageStr != null && !pageStr.trim().isEmpty()) {
                page = Integer.parseInt(pageStr);
                if (page < 1) page = 1;
            }
            if (sizeStr != null && !sizeStr.trim().isEmpty()) {
                size = Integer.parseInt(sizeStr);
                if (size < 1) size = 10;
                if (size > 100) size = 100; // 限制最大页面大小
            }
        } catch (NumberFormatException e) {
            throw ApiException.paramError("分页参数格式错误");
        }
        
        // 模拟从数据库获取用户列表
        List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
        
        int start = (page - 1) * size;
        for (int i = start; i < start + size; i++) {
            Map<String, Object> user = new HashMap<String, Object>();
            user.put("userId", "U" + String.format("%04d", i + 1));
            user.put("userName", "用户" + (i + 1));
            user.put("email", "user" + (i + 1) + "@example.com");
            user.put("status", i % 3 == 0 ? "inactive" : "active");
            user.put("createTime", "2024-01-" + String.format("%02d", (i % 28) + 1) + " 10:00:00");
            
            // 如果有关键字搜索，简单过滤
            if (keyword != null && !keyword.trim().isEmpty()) {
                String userName = (String) user.get("userName");
                String email = (String) user.get("email");
                if (!userName.contains(keyword) && !email.contains(keyword)) {
                    continue;
                }
            }
            
            userList.add(user);
        }
        
        // 构造分页响应
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("list", userList);
        result.put("page", page);
        result.put("size", size);
        result.put("total", 1000); // 模拟总数
        result.put("totalPages", (1000 + size - 1) / size);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            result.put("keyword", keyword);
        }
        
        return BaseResponse.success("获取用户列表成功", result);
    }
    
    /**
     * 创建用户
     */
    public BaseResponse<?> createUser(String requestBody) throws ApiException {
        if (requestBody == null || requestBody.trim().isEmpty()) {
            throw ApiException.paramError("请求体不能为空");
        }
        
        // 解析请求体
        Map<String, Object> userData;
        try {
            userData = JsonUtils.jsonToMap(requestBody);
        } catch (Exception e) {
            throw ApiException.paramError("请求体JSON格式错误");
        }
        
        // 验证必填字段
        String userName = (String) userData.get("userName");
        String email = (String) userData.get("email");
        String phone = (String) userData.get("phone");
        
        if (userName == null || userName.trim().isEmpty()) {
            throw ApiException.paramError("用户名不能为空");
        }
        if (email == null || email.trim().isEmpty()) {
            throw ApiException.paramError("邮箱不能为空");
        }
        
        // 简单的邮箱格式验证
        if (!email.contains("@") || !email.contains(".")) {
            throw ApiException.paramError("邮箱格式不正确");
        }
        
        // 模拟创建用户
        String newUserId = "U" + System.currentTimeMillis();
        
        Map<String, Object> newUser = new HashMap<String, Object>();
        newUser.put("userId", newUserId);
        newUser.put("userName", userName);
        newUser.put("email", email);
        newUser.put("phone", phone);
        newUser.put("status", "active");
        newUser.put("createTime", new java.util.Date().toString());
        
        return BaseResponse.success("创建用户成功", newUser);
    }
    
    /**
     * 更新用户信息
     */
    public BaseResponse<?> updateUser(String userId, String requestBody) throws ApiException {
        if (userId == null || userId.trim().isEmpty()) {
            throw ApiException.paramError("用户ID不能为空");
        }
        
        if (requestBody == null || requestBody.trim().isEmpty()) {
            throw ApiException.paramError("请求体不能为空");
        }
        
        // 解析请求体
        Map<String, Object> updateData;
        try {
            updateData = JsonUtils.jsonToMap(requestBody);
        } catch (Exception e) {
            throw ApiException.paramError("请求体JSON格式错误");
        }
        
        // 模拟更新用户
        Map<String, Object> updatedUser = new HashMap<String, Object>();
        updatedUser.put("userId", userId);
        updatedUser.put("userName", updateData.get("userName"));
        updatedUser.put("email", updateData.get("email"));
        updatedUser.put("phone", updateData.get("phone"));
        updatedUser.put("status", updateData.get("status"));
        updatedUser.put("updateTime", new java.util.Date().toString());
        
        return BaseResponse.success("更新用户成功", updatedUser);
    }
    
    /**
     * 删除用户
     */
    public BaseResponse<?> deleteUser(String userId) throws ApiException {
        if (userId == null || userId.trim().isEmpty()) {
            throw ApiException.paramError("用户ID不能为空");
        }
        
        // 模拟删除用户
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("userId", userId);
        result.put("deleted", true);
        result.put("deleteTime", new java.util.Date().toString());
        
        return BaseResponse.success("删除用户成功", result);
    }
}
