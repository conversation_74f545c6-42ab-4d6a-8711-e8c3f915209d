package com.aisino.a6.http.api;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.aisino.a6.http.util.HttpUtils;
import com.aisino.a6.http.util.JsonUtils;
import com.aisino.a6.http.util.TransactionManager;
import com.aisino.a6.http.annotation.Transactional;

/**
 * 用户API控制器
 * 处理用户相关的HTTP API请求
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class UserApiController {
    
    /**
     * 获取用户信息
     */
    public BaseResponse<?> getUserInfo(String userId) throws ApiException {
        if (userId == null || userId.trim().isEmpty()) {
            throw ApiException.paramError("用户ID不能为空");
        }
        
        // 模拟从数据库获取用户信息
        Map<String, Object> userInfo = new HashMap<String, Object>();
        userInfo.put("userId", userId);
        userInfo.put("userName", "用户" + userId);
        userInfo.put("email", "user" + userId + "@example.com");
        userInfo.put("phone", "138****" + userId.substring(Math.max(0, userId.length() - 4)));
        userInfo.put("status", "active");
        userInfo.put("createTime", "2024-01-01 10:00:00");
        userInfo.put("lastLoginTime", "2024-08-01 09:30:00");
        
        return BaseResponse.success("获取用户信息成功", userInfo);
    }
    
    /**
     * 获取用户列表
     */
    public BaseResponse<?> getUserList(HttpServletRequest request) throws ApiException {
        // 获取分页参数
        String pageStr = request.getParameter("page");
        String sizeStr = request.getParameter("size");
        String keyword = request.getParameter("keyword");
        
        int page = 1;
        int size = 10;
        
        try {
            if (pageStr != null && !pageStr.trim().isEmpty()) {
                page = Integer.parseInt(pageStr);
                if (page < 1) page = 1;
            }
            if (sizeStr != null && !sizeStr.trim().isEmpty()) {
                size = Integer.parseInt(sizeStr);
                if (size < 1) size = 10;
                if (size > 100) size = 100; // 限制最大页面大小
            }
        } catch (NumberFormatException e) {
            throw ApiException.paramError("分页参数格式错误");
        }
        
        // 模拟从数据库获取用户列表
        List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
        
        int start = (page - 1) * size;
        for (int i = start; i < start + size; i++) {
            Map<String, Object> user = new HashMap<String, Object>();
            user.put("userId", "U" + String.format("%04d", i + 1));
            user.put("userName", "用户" + (i + 1));
            user.put("email", "user" + (i + 1) + "@example.com");
            user.put("status", i % 3 == 0 ? "inactive" : "active");
            user.put("createTime", "2024-01-" + String.format("%02d", (i % 28) + 1) + " 10:00:00");
            
            // 如果有关键字搜索，简单过滤
            if (keyword != null && !keyword.trim().isEmpty()) {
                String userName = (String) user.get("userName");
                String email = (String) user.get("email");
                if (!userName.contains(keyword) && !email.contains(keyword)) {
                    continue;
                }
            }
            
            userList.add(user);
        }
        
        // 构造分页响应
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("list", userList);
        result.put("page", page);
        result.put("size", size);
        result.put("total", 1000); // 模拟总数
        result.put("totalPages", (1000 + size - 1) / size);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            result.put("keyword", keyword);
        }
        
        return BaseResponse.success("获取用户列表成功", result);
    }
    
    /**
     * 创建用户（带事务处理）
     */
    @Transactional
    public BaseResponse<?> createUser(String requestBody) throws ApiException {
        if (requestBody == null || requestBody.trim().isEmpty()) {
            throw ApiException.paramError("请求体不能为空");
        }

        // 解析请求体
        Map<String, Object> userData;
        try {
            userData = JsonUtils.jsonToMap(requestBody);
        } catch (Exception e) {
            throw ApiException.paramError("请求体JSON格式错误");
        }

        // 验证必填字段
        String userName = (String) userData.get("userName");
        String email = (String) userData.get("email");
        String phone = (String) userData.get("phone");

        if (userName == null || userName.trim().isEmpty()) {
            throw ApiException.paramError("用户名不能为空");
        }
        if (email == null || email.trim().isEmpty()) {
            throw ApiException.paramError("邮箱不能为空");
        }

        // 简单的邮箱格式验证
        if (!email.contains("@") || !email.contains(".")) {
            throw ApiException.paramError("邮箱格式不正确");
        }

        try {
            // 在事务中创建用户
            String newUserId = createUserInDatabase(userName, email, phone);

            // 创建用户成功后的其他操作（如发送欢迎邮件、创建默认设置等）
            createUserDefaultSettings(newUserId);

            Map<String, Object> newUser = new HashMap<String, Object>();
            newUser.put("userId", newUserId);
            newUser.put("userName", userName);
            newUser.put("email", email);
            newUser.put("phone", phone);
            newUser.put("status", "active");
            newUser.put("createTime", new java.util.Date().toString());

            return BaseResponse.success("创建用户成功", newUser);

        } catch (SQLException e) {
            throw new ApiException("数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            throw new ApiException("创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 在数据库中创建用户
     */
    private String createUserInDatabase(String userName, String email, String phone) throws SQLException {
        String newUserId = "U" + System.currentTimeMillis();

        if (TransactionManager.isTransactionActive()) {
            Connection conn = TransactionManager.getCurrentConnection();

            // 检查邮箱是否已存在
            String checkSql = "SELECT COUNT(*) FROM users WHERE email = ?";
            try (PreparedStatement checkStmt = conn.prepareStatement(checkSql)) {
                checkStmt.setString(1, email);
                try (ResultSet rs = checkStmt.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        throw new SQLException("邮箱已存在: " + email);
                    }
                }
            }

            // 插入新用户
            String insertSql = "INSERT INTO users (user_id, user_name, email, phone, status, create_time) VALUES (?, ?, ?, ?, ?, SYSDATE)";
            try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
                insertStmt.setString(1, newUserId);
                insertStmt.setString(2, userName);
                insertStmt.setString(3, email);
                insertStmt.setString(4, phone);
                insertStmt.setString(5, "active");

                int rowsAffected = insertStmt.executeUpdate();
                if (rowsAffected != 1) {
                    throw new SQLException("插入用户失败，影响行数: " + rowsAffected);
                }
            }

            System.out.println("User created in database: " + newUserId);
        } else {
            // 如果没有事务，模拟创建
            System.out.println("Simulating user creation (no transaction): " + newUserId);
        }

        return newUserId;
    }

    /**
     * 创建用户默认设置
     */
    private void createUserDefaultSettings(String userId) throws SQLException {
        if (TransactionManager.isTransactionActive()) {
            Connection conn = TransactionManager.getCurrentConnection();

            String insertSql = "INSERT INTO user_settings (user_id, setting_key, setting_value, create_time) VALUES (?, ?, ?, SYSDATE)";
            try (PreparedStatement stmt = conn.prepareStatement(insertSql)) {
                // 插入默认语言设置
                stmt.setString(1, userId);
                stmt.setString(2, "language");
                stmt.setString(3, "zh_CN");
                stmt.addBatch();

                // 插入默认主题设置
                stmt.setString(1, userId);
                stmt.setString(2, "theme");
                stmt.setString(3, "default");
                stmt.addBatch();

                // 插入默认时区设置
                stmt.setString(1, userId);
                stmt.setString(2, "timezone");
                stmt.setString(3, "Asia/Shanghai");
                stmt.addBatch();

                int[] results = stmt.executeBatch();
                System.out.println("Created " + results.length + " default settings for user: " + userId);
            }
        } else {
            System.out.println("Simulating default settings creation for user: " + userId);
        }
    }
    
    /**
     * 更新用户信息
     */
    public BaseResponse<?> updateUser(String userId, String requestBody) throws ApiException {
        if (userId == null || userId.trim().isEmpty()) {
            throw ApiException.paramError("用户ID不能为空");
        }
        
        if (requestBody == null || requestBody.trim().isEmpty()) {
            throw ApiException.paramError("请求体不能为空");
        }
        
        // 解析请求体
        Map<String, Object> updateData;
        try {
            updateData = JsonUtils.jsonToMap(requestBody);
        } catch (Exception e) {
            throw ApiException.paramError("请求体JSON格式错误");
        }
        
        // 模拟更新用户
        Map<String, Object> updatedUser = new HashMap<String, Object>();
        updatedUser.put("userId", userId);
        updatedUser.put("userName", updateData.get("userName"));
        updatedUser.put("email", updateData.get("email"));
        updatedUser.put("phone", updateData.get("phone"));
        updatedUser.put("status", updateData.get("status"));
        updatedUser.put("updateTime", new java.util.Date().toString());
        
        return BaseResponse.success("更新用户成功", updatedUser);
    }
    
    /**
     * 删除用户（带事务处理）
     */
    @Transactional
    public BaseResponse<?> deleteUser(String userId) throws ApiException {
        if (userId == null || userId.trim().isEmpty()) {
            throw ApiException.paramError("用户ID不能为空");
        }

        try {
            // 在事务中删除用户
            deleteUserFromDatabase(userId);

            Map<String, Object> result = new HashMap<String, Object>();
            result.put("userId", userId);
            result.put("deleted", true);
            result.put("deleteTime", new java.util.Date().toString());

            return BaseResponse.success("删除用户成功", result);

        } catch (SQLException e) {
            throw new ApiException("数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            throw new ApiException("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 从数据库中删除用户
     */
    private void deleteUserFromDatabase(String userId) throws SQLException {
        if (TransactionManager.isTransactionActive()) {
            Connection conn = TransactionManager.getCurrentConnection();

            // 先删除用户设置
            String deleteSettingsSql = "DELETE FROM user_settings WHERE user_id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(deleteSettingsSql)) {
                stmt.setString(1, userId);
                int settingsDeleted = stmt.executeUpdate();
                System.out.println("Deleted " + settingsDeleted + " settings for user: " + userId);
            }

            // 再删除用户
            String deleteUserSql = "DELETE FROM users WHERE user_id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(deleteUserSql)) {
                stmt.setString(1, userId);
                int rowsAffected = stmt.executeUpdate();
                if (rowsAffected == 0) {
                    throw new SQLException("用户不存在: " + userId);
                }
                System.out.println("Deleted user: " + userId);
            }
        } else {
            System.out.println("Simulating user deletion (no transaction): " + userId);
        }
    }

    /**
     * 批量创建用户（演示事务回滚）
     */
    @Transactional
    public BaseResponse<?> batchCreateUsers(String requestBody) throws ApiException {
        if (requestBody == null || requestBody.trim().isEmpty()) {
            throw ApiException.paramError("请求体不能为空");
        }

        List<Map<String, Object>> userList;
        try {
            userList = JsonUtils.jsonToList(requestBody, Map.class);
        } catch (Exception e) {
            throw ApiException.paramError("请求体JSON格式错误");
        }

        if (userList == null || userList.isEmpty()) {
            throw ApiException.paramError("用户列表不能为空");
        }

        List<String> createdUserIds = new ArrayList<String>();

        try {
            for (int i = 0; i < userList.size(); i++) {
                Map<String, Object> userData = userList.get(i);
                String userName = (String) userData.get("userName");
                String email = (String) userData.get("email");
                String phone = (String) userData.get("phone");

                // 验证数据
                if (userName == null || userName.trim().isEmpty()) {
                    throw new ApiException("第" + (i + 1) + "个用户的用户名不能为空");
                }
                if (email == null || email.trim().isEmpty()) {
                    throw new ApiException("第" + (i + 1) + "个用户的邮箱不能为空");
                }

                // 模拟某种业务规则：如果用户名包含"error"，则抛出异常
                if (userName.toLowerCase().contains("error")) {
                    throw new ApiException("用户名不能包含'error'关键字: " + userName);
                }

                // 创建用户
                String userId = createUserInDatabase(userName, email, phone);
                createUserDefaultSettings(userId);
                createdUserIds.add(userId);

                System.out.println("Batch created user " + (i + 1) + "/" + userList.size() + ": " + userId);
            }

            Map<String, Object> result = new HashMap<String, Object>();
            result.put("createdCount", createdUserIds.size());
            result.put("createdUserIds", createdUserIds);
            result.put("createTime", new java.util.Date().toString());

            return BaseResponse.success("批量创建用户成功", result);

        } catch (Exception e) {
            // 如果发生异常，事务会自动回滚，所有已创建的用户都会被撤销
            System.err.println("Batch user creation failed, transaction will be rolled back: " + e.getMessage());
            throw new ApiException("批量创建用户失败: " + e.getMessage());
        }
    }
}
