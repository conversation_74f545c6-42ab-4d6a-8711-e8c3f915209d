package com.aisino.a6.http.init;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import com.aisino.a6.http.util.DatabaseConnectionManager;

/**
 * HTTP API模块初始化器
 * 在Web应用启动时初始化必要的组件
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class HttpApiInitializer implements ServletContextListener {
    
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        System.out.println("=== HTTP API Module Initializing ===");
        
        try {
            // 初始化数据库连接管理器
            initializeDatabaseConnectionManager();
            
            // 初始化其他组件
            initializeOtherComponents();
            
            System.out.println("=== HTTP API Module Initialized Successfully ===");
            
        } catch (Exception e) {
            System.err.println("=== HTTP API Module Initialization Failed ===");
            e.printStackTrace();
        }
    }
    
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        System.out.println("=== HTTP API Module Shutting Down ===");
        
        try {
            // 清理资源
            cleanup();
            
            System.out.println("=== HTTP API Module Shutdown Complete ===");
            
        } catch (Exception e) {
            System.err.println("Error during HTTP API module shutdown: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 初始化数据库连接管理器
     */
    private void initializeDatabaseConnectionManager() {
        try {
            System.out.println("Initializing Database Connection Manager...");
            
            // 从系统属性或配置文件读取数据库配置
            String useA6Platform = System.getProperty("http.api.use.a6.platform", "true");
            String defaultDataSource = System.getProperty("http.api.default.datasource", "default");
            
            // 备用数据库配置
            String backupDriver = System.getProperty("http.api.backup.driver", "oracle.jdbc.driver.OracleDriver");
            String backupUrl = System.getProperty("http.api.backup.url", "***********************************");
            String backupUsername = System.getProperty("http.api.backup.username", "a6user");
            String backupPassword = System.getProperty("http.api.backup.password", "a6pass");
            
            // 配置数据库连接管理器
            DatabaseConnectionManager.setUseA6Platform(Boolean.parseBoolean(useA6Platform));
            DatabaseConnectionManager.setDefaultDataSource(defaultDataSource);
            DatabaseConnectionManager.setBackupConnectionConfig(backupDriver, backupUrl, backupUsername, backupPassword);
            
            // 初始化连接管理器
            DatabaseConnectionManager.initialize();
            
            System.out.println("Database Connection Manager initialized successfully");
            
        } catch (Exception e) {
            System.err.println("Failed to initialize Database Connection Manager: " + e.getMessage());
            throw new RuntimeException("Database initialization failed", e);
        }
    }
    
    /**
     * 初始化其他组件
     */
    private void initializeOtherComponents() {
        try {
            System.out.println("Initializing other HTTP API components...");
            
            // 初始化缓存（如果需要）
            initializeCache();
            
            // 初始化监控（如果需要）
            initializeMonitoring();
            
            // 初始化安全组件（如果需要）
            initializeSecurity();
            
            System.out.println("Other HTTP API components initialized successfully");
            
        } catch (Exception e) {
            System.err.println("Failed to initialize other components: " + e.getMessage());
            // 这里不抛出异常，允许应用继续启动
        }
    }
    
    /**
     * 初始化缓存
     */
    private void initializeCache() {
        try {
            String enableCache = System.getProperty("http.api.cache.enable", "true");
            if (Boolean.parseBoolean(enableCache)) {
                System.out.println("Cache enabled for HTTP API");
                // 这里可以初始化缓存组件
            } else {
                System.out.println("Cache disabled for HTTP API");
            }
        } catch (Exception e) {
            System.err.println("Failed to initialize cache: " + e.getMessage());
        }
    }
    
    /**
     * 初始化监控
     */
    private void initializeMonitoring() {
        try {
            String enableMonitoring = System.getProperty("http.api.monitoring.enable", "true");
            if (Boolean.parseBoolean(enableMonitoring)) {
                System.out.println("Monitoring enabled for HTTP API");
                // 这里可以初始化监控组件
            } else {
                System.out.println("Monitoring disabled for HTTP API");
            }
        } catch (Exception e) {
            System.err.println("Failed to initialize monitoring: " + e.getMessage());
        }
    }
    
    /**
     * 初始化安全组件
     */
    private void initializeSecurity() {
        try {
            String enableAuth = System.getProperty("http.api.security.enable", "false");
            if (Boolean.parseBoolean(enableAuth)) {
                System.out.println("Security enabled for HTTP API");
                // 这里可以初始化安全组件
            } else {
                System.out.println("Security disabled for HTTP API");
            }
        } catch (Exception e) {
            System.err.println("Failed to initialize security: " + e.getMessage());
        }
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            System.out.println("Cleaning up HTTP API resources...");
            
            // 清理数据库连接池（如果有）
            // 清理缓存（如果有）
            // 清理监控资源（如果有）
            
            System.out.println("HTTP API resources cleaned up successfully");
            
        } catch (Exception e) {
            System.err.println("Error during cleanup: " + e.getMessage());
        }
    }
    
    /**
     * 获取初始化状态信息
     */
    public static String getInitializationStatus() {
        StringBuilder status = new StringBuilder();
        status.append("HTTP API Module Status:\n");
        
        try {
            // 检查数据库连接
            if (DatabaseConnectionManager.testConnection()) {
                status.append("- Database Connection: OK\n");
                status.append("- Connection Info: ").append(DatabaseConnectionManager.getConnectionInfo()).append("\n");
            } else {
                status.append("- Database Connection: FAILED\n");
            }
            
            // 检查其他组件状态
            status.append("- Cache: ").append(System.getProperty("http.api.cache.enable", "true")).append("\n");
            status.append("- Monitoring: ").append(System.getProperty("http.api.monitoring.enable", "true")).append("\n");
            status.append("- Security: ").append(System.getProperty("http.api.security.enable", "false")).append("\n");
            
        } catch (Exception e) {
            status.append("- Error getting status: ").append(e.getMessage()).append("\n");
        }
        
        return status.toString();
    }
}
