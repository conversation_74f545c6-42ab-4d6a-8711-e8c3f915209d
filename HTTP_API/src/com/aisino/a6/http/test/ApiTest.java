package com.aisino.a6.http.test;

import com.aisino.a6.http.api.BaseResponse;
import com.aisino.a6.http.api.UserApiController;
import com.aisino.a6.http.api.SystemApiController;
import com.aisino.a6.http.api.ApiException;
import com.aisino.a6.http.util.JsonUtils;

/**
 * HTTP API测试类
 * 用于测试API接口的基本功能
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class ApiTest {
    
    public static void main(String[] args) {
        System.out.println("=== A6 HTTP API 测试开始 ===");
        
        // 测试JSON工具类
        testJsonUtils();
        
        // 测试用户API
        testUserApi();
        
        // 测试系统API
        testSystemApi();
        
        // 测试异常处理
        testExceptionHandling();
        
        System.out.println("=== A6 HTTP API 测试完成 ===");
    }
    
    /**
     * 测试JSON工具类
     */
    private static void testJsonUtils() {
        System.out.println("\n--- 测试JSON工具类 ---");
        
        try {
            // 测试对象转JSON
            BaseResponse<String> response = BaseResponse.success("测试数据");
            String json = JsonUtils.toJson(response);
            System.out.println("对象转JSON: " + json);
            
            // 测试JSON转对象
            BaseResponse<?> parsedResponse = JsonUtils.fromJson(json, BaseResponse.class);
            System.out.println("JSON转对象: " + parsedResponse);
            
            // 测试JSON格式化
            String formattedJson = JsonUtils.formatJson(json);
            System.out.println("格式化JSON:\n" + formattedJson);
            
            System.out.println("JSON工具类测试通过");
            
        } catch (Exception e) {
            System.err.println("JSON工具类测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用户API
     */
    private static void testUserApi() {
        System.out.println("\n--- 测试用户API ---");
        
        UserApiController userController = new UserApiController();
        
        try {
            // 测试获取用户信息
            BaseResponse<?> userInfoResponse = userController.getUserInfo("U001");
            System.out.println("获取用户信息: " + JsonUtils.toJson(userInfoResponse));
            
            // 测试创建用户
            String createUserJson = "{\"userName\":\"测试用户\",\"email\":\"<EMAIL>\",\"phone\":\"13800138000\"}";
            BaseResponse<?> createResponse = userController.createUser(createUserJson);
            System.out.println("创建用户: " + JsonUtils.toJson(createResponse));
            
            System.out.println("用户API测试通过");
            
        } catch (Exception e) {
            System.err.println("用户API测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试系统API
     */
    private static void testSystemApi() {
        System.out.println("\n--- 测试系统API ---");
        
        SystemApiController systemController = new SystemApiController();
        
        try {
            // 测试获取系统信息
            BaseResponse<?> systemInfoResponse = systemController.getSystemInfo();
            System.out.println("获取系统信息: " + JsonUtils.toJson(systemInfoResponse));
            
            // 测试获取系统时间
            BaseResponse<?> timeResponse = systemController.getSystemTime();
            System.out.println("获取系统时间: " + JsonUtils.toJson(timeResponse));
            
            // 测试获取系统配置
            BaseResponse<?> configResponse = systemController.getSystemConfig();
            System.out.println("获取系统配置: " + JsonUtils.toJson(configResponse));
            
            System.out.println("系统API测试通过");
            
        } catch (Exception e) {
            System.err.println("系统API测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试异常处理
     */
    private static void testExceptionHandling() {
        System.out.println("\n--- 测试异常处理 ---");
        
        UserApiController userController = new UserApiController();
        
        try {
            // 测试参数错误异常
            try {
                userController.getUserInfo(null);
            } catch (ApiException e) {
                System.out.println("参数错误异常: " + e.getErrorMessage());
                BaseResponse<?> errorResponse = BaseResponse.error(e.getErrorCode(), e.getErrorMessage());
                System.out.println("错误响应: " + JsonUtils.toJson(errorResponse));
            }
            
            // 测试JSON格式错误异常
            try {
                userController.createUser("invalid json");
            } catch (ApiException e) {
                System.out.println("JSON格式错误异常: " + e.getErrorMessage());
            }
            
            // 测试各种错误响应
            BaseResponse<?> paramErrorResponse = BaseResponse.paramError("参数错误");
            BaseResponse<?> authErrorResponse = BaseResponse.authError("认证失败");
            BaseResponse<?> generalErrorResponse = BaseResponse.error("一般错误");
            
            System.out.println("参数错误响应: " + JsonUtils.toJson(paramErrorResponse));
            System.out.println("认证错误响应: " + JsonUtils.toJson(authErrorResponse));
            System.out.println("一般错误响应: " + JsonUtils.toJson(generalErrorResponse));
            
            System.out.println("异常处理测试通过");
            
        } catch (Exception e) {
            System.err.println("异常处理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
