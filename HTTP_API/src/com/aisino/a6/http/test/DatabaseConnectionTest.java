package com.aisino.a6.http.test;

import java.sql.Connection;

import com.aisino.a6.http.util.DatabaseConnectionManager;
import com.aisino.a6.http.util.TransactionManager;

/**
 * 数据库连接测试类
 * 专门测试数据库连接和事务功能
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("=== 数据库连接测试开始 ===");
        
        // 测试数据库连接管理器
        testDatabaseConnectionManager();
        
        // 测试事务管理器
        testTransactionManager();
        
        System.out.println("=== 数据库连接测试完成 ===");
    }
    
    /**
     * 测试数据库连接管理器
     */
    private static void testDatabaseConnectionManager() {
        System.out.println("\n--- 测试数据库连接管理器 ---");
        
        try {
            // 初始化连接管理器
            System.out.println("初始化数据库连接管理器...");
            DatabaseConnectionManager.initialize();
            
            // 测试连接
            System.out.println("测试数据库连接...");
            boolean connected = DatabaseConnectionManager.testConnection();
            System.out.println("连接测试结果: " + (connected ? "成功" : "失败"));
            
            if (connected) {
                // 获取连接信息
                String connectionInfo = DatabaseConnectionManager.getConnectionInfo();
                System.out.println("连接信息: " + connectionInfo);
                
                // 测试获取连接
                System.out.println("获取数据库连接...");
                Connection conn = DatabaseConnectionManager.getConnection();
                if (conn != null && !conn.isClosed()) {
                    System.out.println("成功获取连接: " + conn.getClass().getName());
                    System.out.println("连接哈希码: " + conn.hashCode());
                    System.out.println("自动提交模式: " + conn.getAutoCommit());
                    conn.close();
                    System.out.println("连接已关闭");
                } else {
                    System.err.println("获取的连接无效");
                }
            }
            
            System.out.println("数据库连接管理器测试通过");
            
        } catch (Exception e) {
            System.err.println("数据库连接管理器测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试事务管理器
     */
    private static void testTransactionManager() {
        System.out.println("\n--- 测试事务管理器 ---");
        
        try {
            // 测试基本事务操作
            testBasicTransaction();
            
            // 测试事务回滚
            testTransactionRollback();
            
            // 测试事务操作接口
            testTransactionOperation();
            
            System.out.println("事务管理器测试通过");
            
        } catch (Exception e) {
            System.err.println("事务管理器测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试基本事务操作
     */
    private static void testBasicTransaction() throws Exception {
        System.out.println("\n测试基本事务操作...");
        
        // 开始事务
        System.out.println("开始事务...");
        TransactionManager.beginTransaction();
        System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
        
        // 获取连接
        Connection conn = TransactionManager.getCurrentConnection();
        System.out.println("获取事务连接: " + conn.hashCode());
        System.out.println("自动提交模式: " + conn.getAutoCommit());
        
        // 模拟一些操作
        Thread.sleep(100);
        
        // 提交事务
        System.out.println("提交事务...");
        TransactionManager.commitTransaction();
        System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
        
        System.out.println("基本事务操作测试成功");
    }
    
    /**
     * 测试事务回滚
     */
    private static void testTransactionRollback() throws Exception {
        System.out.println("\n测试事务回滚...");
        
        // 开始事务
        System.out.println("开始事务...");
        TransactionManager.beginTransaction();
        System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
        
        // 获取连接
        Connection conn = TransactionManager.getCurrentConnection();
        System.out.println("获取事务连接: " + conn.hashCode());
        
        // 模拟一些操作
        Thread.sleep(100);
        
        // 回滚事务
        System.out.println("回滚事务...");
        TransactionManager.rollbackTransaction();
        System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
        
        System.out.println("事务回滚测试成功");
    }
    
    /**
     * 测试事务操作接口
     */
    private static void testTransactionOperation() throws Exception {
        System.out.println("\n测试事务操作接口...");
        
        // 成功场景
        String result1 = TransactionManager.executeInTransaction(
            new TransactionManager.TransactionOperation<String>() {
                @Override
                public String execute() throws Exception {
                    System.out.println("执行事务操作 - 成功场景");
                    System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
                    Thread.sleep(50);
                    return "操作成功";
                }
            }
        );
        System.out.println("事务操作结果: " + result1);
        
        // 失败场景
        try {
            TransactionManager.executeInTransaction(
                new TransactionManager.TransactionOperation<String>() {
                    @Override
                    public String execute() throws Exception {
                        System.out.println("执行事务操作 - 失败场景");
                        System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
                        Thread.sleep(50);
                        throw new RuntimeException("模拟业务异常");
                    }
                }
            );
            System.err.println("预期异常未发生");
        } catch (Exception e) {
            System.out.println("预期的异常发生: " + e.getMessage());
            System.out.println("事务已自动回滚");
        }
        
        System.out.println("事务操作接口测试成功");
    }
    
    /**
     * 测试并发事务
     */
    private static void testConcurrentTransactions() {
        System.out.println("\n测试并发事务...");
        
        // 创建多个线程同时执行事务
        Thread[] threads = new Thread[3];
        
        for (int i = 0; i < threads.length; i++) {
            final int threadId = i;
            threads[i] = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        String result = TransactionManager.executeInTransaction(
                            new TransactionManager.TransactionOperation<String>() {
                                @Override
                                public String execute() throws Exception {
                                    System.out.println("线程 " + threadId + " 执行事务操作");
                                    System.out.println("线程 " + threadId + " 事务状态: " + TransactionManager.getTransactionStatus());
                                    Thread.sleep(100);
                                    return "线程 " + threadId + " 完成";
                                }
                            }
                        );
                        System.out.println("线程 " + threadId + " 结果: " + result);
                    } catch (Exception e) {
                        System.err.println("线程 " + threadId + " 异常: " + e.getMessage());
                    }
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                System.err.println("等待线程完成时被中断: " + e.getMessage());
            }
        }
        
        System.out.println("并发事务测试完成");
    }
}
