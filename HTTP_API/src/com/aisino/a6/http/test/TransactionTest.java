package com.aisino.a6.http.test;

import com.aisino.a6.http.api.BaseResponse;
import com.aisino.a6.http.api.UserApiController;
import com.aisino.a6.http.api.ApiException;
import com.aisino.a6.http.util.JsonUtils;
import com.aisino.a6.http.util.TransactionManager;

/**
 * 事务功能测试类
 * 测试事务的提交和回滚功能
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class TransactionTest {
    
    public static void main(String[] args) {
        System.out.println("=== A6 HTTP API 事务测试开始 ===");
        
        // 测试事务管理器基本功能
        testTransactionManager();
        
        // 测试单用户创建（成功场景）
        testSingleUserCreation();
        
        // 测试批量用户创建（成功场景）
        testBatchUserCreationSuccess();
        
        // 测试批量用户创建（失败回滚场景）
        testBatchUserCreationRollback();
        
        // 测试用户删除
        testUserDeletion();
        
        System.out.println("=== A6 HTTP API 事务测试完成 ===");
    }
    
    /**
     * 测试事务管理器基本功能
     */
    private static void testTransactionManager() {
        System.out.println("\n--- 测试事务管理器基本功能 ---");
        
        try {
            // 测试事务开始
            System.out.println("开始事务...");
            TransactionManager.beginTransaction();
            System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
            
            // 模拟一些操作
            Thread.sleep(100);
            
            // 测试事务提交
            System.out.println("提交事务...");
            TransactionManager.commitTransaction();
            System.out.println("事务状态: " + TransactionManager.getTransactionStatus());
            
            System.out.println("事务管理器基本功能测试通过");
            
        } catch (Exception e) {
            System.err.println("事务管理器测试失败: " + e.getMessage());
            TransactionManager.forceCleanup();
        }
    }
    
    /**
     * 测试单用户创建
     */
    private static void testSingleUserCreation() {
        System.out.println("\n--- 测试单用户创建（事务提交） ---");
        
        try {
            UserApiController userController = new UserApiController();
            
            String userJson = "{\"userName\":\"测试用户001\",\"email\":\"<EMAIL>\",\"phone\":\"13800138001\"}";
            
            // 在事务中执行
            BaseResponse<?> response = TransactionManager.executeInTransaction(
                new TransactionManager.TransactionOperation<BaseResponse<?>>() {
                    @Override
                    public BaseResponse<?> execute() throws Exception {
                        return userController.createUser(userJson);
                    }
                }
            );
            
            System.out.println("创建用户结果: " + JsonUtils.toJson(response));
            System.out.println("单用户创建测试通过");
            
        } catch (Exception e) {
            System.err.println("单用户创建测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试批量用户创建（成功场景）
     */
    private static void testBatchUserCreationSuccess() {
        System.out.println("\n--- 测试批量用户创建（成功场景） ---");
        
        try {
            UserApiController userController = new UserApiController();
            
            String batchUserJson = "[" +
                "{\"userName\":\"批量用户001\",\"email\":\"<EMAIL>\",\"phone\":\"13800138001\"}," +
                "{\"userName\":\"批量用户002\",\"email\":\"<EMAIL>\",\"phone\":\"13800138002\"}," +
                "{\"userName\":\"批量用户003\",\"email\":\"<EMAIL>\",\"phone\":\"13800138003\"}" +
                "]";
            
            // 在事务中执行
            BaseResponse<?> response = TransactionManager.executeInTransaction(
                new TransactionManager.TransactionOperation<BaseResponse<?>>() {
                    @Override
                    public BaseResponse<?> execute() throws Exception {
                        return userController.batchCreateUsers(batchUserJson);
                    }
                }
            );
            
            System.out.println("批量创建用户结果: " + JsonUtils.toJson(response));
            System.out.println("批量用户创建成功测试通过");
            
        } catch (Exception e) {
            System.err.println("批量用户创建成功测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试批量用户创建（失败回滚场景）
     */
    private static void testBatchUserCreationRollback() {
        System.out.println("\n--- 测试批量用户创建（失败回滚场景） ---");
        
        try {
            UserApiController userController = new UserApiController();
            
            // 注意：第二个用户名包含"error"，会触发业务异常
            String batchUserJson = "[" +
                "{\"userName\":\"正常用户001\",\"email\":\"<EMAIL>\",\"phone\":\"13800138001\"}," +
                "{\"userName\":\"error用户\",\"email\":\"<EMAIL>\",\"phone\":\"13800138002\"}," +
                "{\"userName\":\"正常用户003\",\"email\":\"<EMAIL>\",\"phone\":\"13800138003\"}" +
                "]";
            
            // 在事务中执行
            BaseResponse<?> response = TransactionManager.executeInTransaction(
                new TransactionManager.TransactionOperation<BaseResponse<?>>() {
                    @Override
                    public BaseResponse<?> execute() throws Exception {
                        return userController.batchCreateUsers(batchUserJson);
                    }
                }
            );
            
            System.out.println("意外成功: " + JsonUtils.toJson(response));
            System.err.println("批量用户创建回滚测试失败：应该抛出异常但没有");
            
        } catch (Exception e) {
            System.out.println("预期的异常发生: " + e.getMessage());
            System.out.println("事务已回滚，所有操作都被撤销");
            System.out.println("批量用户创建回滚测试通过");
        }
    }
    
    /**
     * 测试用户删除
     */
    private static void testUserDeletion() {
        System.out.println("\n--- 测试用户删除（事务提交） ---");
        
        try {
            UserApiController userController = new UserApiController();
            
            String userId = "U001";
            
            // 在事务中执行
            BaseResponse<?> response = TransactionManager.executeInTransaction(
                new TransactionManager.TransactionOperation<BaseResponse<?>>() {
                    @Override
                    public BaseResponse<?> execute() throws Exception {
                        return userController.deleteUser(userId);
                    }
                }
            );
            
            System.out.println("删除用户结果: " + JsonUtils.toJson(response));
            System.out.println("用户删除测试通过");
            
        } catch (Exception e) {
            System.err.println("用户删除测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试嵌套事务（演示事务传播）
     */
    private static void testNestedTransaction() {
        System.out.println("\n--- 测试嵌套事务 ---");
        
        try {
            // 外层事务
            TransactionManager.executeInTransaction(
                new TransactionManager.TransactionOperation<Void>() {
                    @Override
                    public Void execute() throws Exception {
                        System.out.println("外层事务开始");
                        
                        // 内层事务
                        try {
                            TransactionManager.executeInTransaction(
                                new TransactionManager.TransactionOperation<Void>() {
                                    @Override
                                    public Void execute() throws Exception {
                                        System.out.println("内层事务开始");
                                        // 模拟一些操作
                                        Thread.sleep(50);
                                        System.out.println("内层事务完成");
                                        return null;
                                    }
                                }
                            );
                        } catch (Exception e) {
                            System.out.println("内层事务异常: " + e.getMessage());
                        }
                        
                        System.out.println("外层事务完成");
                        return null;
                    }
                }
            );
            
            System.out.println("嵌套事务测试通过");
            
        } catch (Exception e) {
            System.err.println("嵌套事务测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
