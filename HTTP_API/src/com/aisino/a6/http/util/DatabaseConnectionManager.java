package com.aisino.a6.http.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

import com.aisino.platform.db.DbSvr;

/**
 * 数据库连接管理器
 * 提供多种方式获取数据库连接，兼容A6平台的不同版本
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class DatabaseConnectionManager {
    
    private static String defaultDataSource = "default";
    private static boolean useA6Platform = true;
    
    // 备用数据库连接配置
    private static String backupDriverClass = "oracle.jdbc.driver.OracleDriver";
    private static String backupUrl = "***********************************";
    private static String backupUsername = "a6user";
    private static String backupPassword = "a6pass";
    
    /**
     * 获取数据库连接
     * 尝试多种方式获取连接，确保兼容性
     */
    public static Connection getConnection() throws SQLException {
        if (useA6Platform) {
            return getA6PlatformConnection();
        } else {
            return getDirectConnection();
        }
    }
    
    /**
     * 尝试通过A6平台获取连接
     */
    private static Connection getA6PlatformConnection() throws SQLException {
        Connection conn = null;
        Exception lastException = null;
        
        // 尝试方式1: 无参数调用
        try {
            conn = DbSvr.getConnection();
            if (conn != null && !conn.isClosed()) {
                System.out.println("Successfully got connection using DbSvr.getConnection()");
                return conn;
            }
        } catch (Exception e) {
            lastException = e;
            System.out.println("DbSvr.getConnection() failed: " + e.getMessage());
        }
        
        // 尝试方式2: 使用默认数据源名称
        try {
            conn = DbSvr.getConnection(defaultDataSource);
            if (conn != null && !conn.isClosed()) {
                System.out.println("Successfully got connection using DbSvr.getConnection(\"" + defaultDataSource + "\")");
                return conn;
            }
        } catch (Exception e) {
            lastException = e;
            System.out.println("DbSvr.getConnection(\"" + defaultDataSource + "\") failed: " + e.getMessage());
        }
        
        // 尝试方式3: 使用空字符串
        try {
            conn = DbSvr.getConnection("");
            if (conn != null && !conn.isClosed()) {
                System.out.println("Successfully got connection using DbSvr.getConnection(\"\")");
                return conn;
            }
        } catch (Exception e) {
            lastException = e;
            System.out.println("DbSvr.getConnection(\"\") failed: " + e.getMessage());
        }
        
        // 尝试方式4: 使用null
        try {
            conn = DbSvr.getConnection(null);
            if (conn != null && !conn.isClosed()) {
                System.out.println("Successfully got connection using DbSvr.getConnection(null)");
                return conn;
            }
        } catch (Exception e) {
            lastException = e;
            System.out.println("DbSvr.getConnection(null) failed: " + e.getMessage());
        }
        
        // 如果A6平台方式都失败，尝试直接连接
        System.out.println("All A6 platform connection methods failed, trying direct connection...");
        try {
            return getDirectConnection();
        } catch (Exception e) {
            System.err.println("Direct connection also failed: " + e.getMessage());
            if (lastException != null) {
                throw new SQLException("无法获取数据库连接，A6平台连接失败: " + lastException.getMessage(), lastException);
            } else {
                throw new SQLException("无法获取数据库连接", e);
            }
        }
    }
    
    /**
     * 直接获取数据库连接（不通过A6平台）
     */
    private static Connection getDirectConnection() throws SQLException {
        try {
            // 加载数据库驱动
            Class.forName(backupDriverClass);
            
            // 创建连接属性
            Properties props = new Properties();
            props.setProperty("user", backupUsername);
            props.setProperty("password", backupPassword);
            props.setProperty("oracle.jdbc.ReadTimeout", "30000");
            
            // 获取连接
            Connection conn = DriverManager.getConnection(backupUrl, props);
            System.out.println("Successfully got direct database connection");
            return conn;
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("数据库驱动未找到: " + backupDriverClass, e);
        } catch (SQLException e) {
            throw new SQLException("直接数据库连接失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 设置默认数据源名称
     */
    public static void setDefaultDataSource(String dataSource) {
        defaultDataSource = dataSource;
    }
    
    /**
     * 设置是否使用A6平台连接
     */
    public static void setUseA6Platform(boolean use) {
        useA6Platform = use;
    }
    
    /**
     * 设置备用数据库连接配置
     */
    public static void setBackupConnectionConfig(String driverClass, String url, String username, String password) {
        backupDriverClass = driverClass;
        backupUrl = url;
        backupUsername = username;
        backupPassword = password;
    }
    
    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        try {
            Connection conn = getConnection();
            if (conn != null && !conn.isClosed()) {
                conn.close();
                return true;
            }
        } catch (Exception e) {
            System.err.println("Database connection test failed: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 获取连接信息
     */
    public static String getConnectionInfo() {
        try {
            Connection conn = getConnection();
            if (conn != null && !conn.isClosed()) {
                String info = String.format("Database: %s, URL: %s, AutoCommit: %s", 
                    conn.getMetaData().getDatabaseProductName(),
                    conn.getMetaData().getURL(),
                    conn.getAutoCommit());
                conn.close();
                return info;
            }
        } catch (Exception e) {
            return "Failed to get connection info: " + e.getMessage();
        }
        return "No connection available";
    }
    
    /**
     * 初始化连接管理器
     * 从配置文件读取数据库配置
     */
    public static void initialize() {
        try {
            // 这里可以从配置文件读取数据库配置
            // Properties config = loadConfig();
            // setBackupConnectionConfig(...);
            
            System.out.println("DatabaseConnectionManager initialized");
            System.out.println("Use A6 Platform: " + useA6Platform);
            System.out.println("Default DataSource: " + defaultDataSource);
            System.out.println("Backup URL: " + backupUrl);
            
            // 测试连接
            if (testConnection()) {
                System.out.println("Database connection test passed");
            } else {
                System.err.println("Database connection test failed");
            }
            
        } catch (Exception e) {
            System.err.println("Failed to initialize DatabaseConnectionManager: " + e.getMessage());
        }
    }
}
