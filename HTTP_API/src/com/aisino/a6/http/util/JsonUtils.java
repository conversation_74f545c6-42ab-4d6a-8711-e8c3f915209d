package com.aisino.a6.http.util;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;

/**
 * JSON工具类
 * 使用Gson进行JSON序列化和反序列化
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class JsonUtils {
    
    private static final Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .create();
    
    private static final JsonParser jsonParser = new JsonParser();
    
    /**
     * 对象转JSON字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return gson.toJson(obj);
        } catch (Exception e) {
            throw new RuntimeException("对象转JSON失败", e);
        }
    }
    
    /**
     * JSON字符串转对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return gson.fromJson(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("JSON转对象失败", e);
        }
    }
    
    /**
     * JSON字符串转对象（支持泛型）
     */
    public static <T> T fromJson(String json, Type type) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return gson.fromJson(json, type);
        } catch (Exception e) {
            throw new RuntimeException("JSON转对象失败", e);
        }
    }
    
    /**
     * JSON字符串转Map
     */
    public static Map<String, Object> jsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<String, Object>();
        }
        try {
            Type type = new TypeToken<Map<String, Object>>(){}.getType();
            return gson.fromJson(json, type);
        } catch (Exception e) {
            throw new RuntimeException("JSON转Map失败", e);
        }
    }
    
    /**
     * JSON字符串转List
     */
    public static <T> List<T> jsonToList(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<T>();
        }
        try {
            Type type = new TypeToken<List<T>>(){}.getType();
            return gson.fromJson(json, type);
        } catch (Exception e) {
            throw new RuntimeException("JSON转List失败", e);
        }
    }
    
    /**
     * 判断字符串是否为有效的JSON
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            jsonParser.parse(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 格式化JSON字符串（美化输出）
     */
    public static String formatJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return json;
        }
        try {
            JsonElement jsonElement = jsonParser.parse(json);
            Gson prettyGson = new GsonBuilder()
                    .setPrettyPrinting()
                    .setDateFormat("yyyy-MM-dd HH:mm:ss")
                    .create();
            return prettyGson.toJson(jsonElement);
        } catch (Exception e) {
            throw new RuntimeException("JSON格式化失败", e);
        }
    }
    
    /**
     * 从JSON字符串中获取指定字段的值
     */
    public static String getJsonValue(String json, String fieldName) {
        if (json == null || json.trim().isEmpty() || fieldName == null) {
            return null;
        }
        try {
            Map<String, Object> map = jsonToMap(json);
            Object value = map.get(fieldName);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }
}
