package com.aisino.a6.http.util;

import java.sql.Connection;
import java.sql.SQLException;

import com.aisino.platform.db.DbSvr;

/**
 * 事务管理工具类
 * 提供事务的开始、提交、回滚功能
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class TransactionManager {
    
    private static final ThreadLocal<Connection> connectionHolder = new ThreadLocal<Connection>();
    private static final ThreadLocal<Boolean> transactionActive = new ThreadLocal<Boolean>();
    
    /**
     * 开始事务
     */
    public static void beginTransaction() throws SQLException {
        if (isTransactionActive()) {
            throw new IllegalStateException("Transaction is already active");
        }
        
        try {
            // 使用DatabaseConnectionManager获取连接
            Connection conn = DatabaseConnectionManager.getConnection();

            if (conn == null) {
                throw new SQLException("获取到的数据库连接为null");
            }

            conn.setAutoCommit(false);
            connectionHolder.set(conn);
            transactionActive.set(true);

            System.out.println("Transaction started - Connection: " + conn.hashCode());
        } catch (SQLException e) {
            throw e;
        } catch (Exception e) {
            throw new SQLException("Failed to begin transaction", e);
        }
    }
    
    /**
     * 提交事务
     */
    public static void commitTransaction() throws SQLException {
        if (!isTransactionActive()) {
            throw new IllegalStateException("No active transaction to commit");
        }
        
        Connection conn = connectionHolder.get();
        try {
            if (conn != null && !conn.isClosed()) {
                conn.commit();
                System.out.println("Transaction committed - Connection: " + conn.hashCode());
            }
        } catch (SQLException e) {
            System.err.println("Failed to commit transaction: " + e.getMessage());
            throw e;
        } finally {
            closeConnection();
        }
    }
    
    /**
     * 回滚事务
     */
    public static void rollbackTransaction() {
        if (!isTransactionActive()) {
            System.out.println("No active transaction to rollback");
            return;
        }
        
        Connection conn = connectionHolder.get();
        try {
            if (conn != null && !conn.isClosed()) {
                conn.rollback();
                System.out.println("Transaction rolled back - Connection: " + conn.hashCode());
            }
        } catch (SQLException e) {
            System.err.println("Failed to rollback transaction: " + e.getMessage());
        } finally {
            closeConnection();
        }
    }
    
    /**
     * 获取当前事务的数据库连接
     */
    public static Connection getCurrentConnection() throws SQLException {
        if (!isTransactionActive()) {
            throw new IllegalStateException("No active transaction");
        }
        
        Connection conn = connectionHolder.get();
        if (conn == null || conn.isClosed()) {
            throw new SQLException("Transaction connection is not available");
        }
        
        return conn;
    }
    
    /**
     * 检查是否有活动的事务
     */
    public static boolean isTransactionActive() {
        Boolean active = transactionActive.get();
        return active != null && active;
    }
    
    /**
     * 关闭连接并清理ThreadLocal
     */
    private static void closeConnection() {
        Connection conn = connectionHolder.get();
        try {
            if (conn != null && !conn.isClosed()) {
                conn.setAutoCommit(true); // 恢复自动提交
                conn.close();
                System.out.println("Connection closed - Connection: " + conn.hashCode());
            }
        } catch (SQLException e) {
            System.err.println("Failed to close connection: " + e.getMessage());
        } finally {
            connectionHolder.remove();
            transactionActive.remove();
        }
    }
    
    /**
     * 执行带事务的操作
     * 
     * @param operation 要执行的操作
     * @return 操作结果
     * @throws Exception 操作异常
     */
    public static <T> T executeInTransaction(TransactionOperation<T> operation) throws Exception {
        beginTransaction();
        try {
            T result = operation.execute();
            commitTransaction();
            return result;
        } catch (Exception e) {
            rollbackTransaction();
            throw e;
        }
    }
    
    /**
     * 事务操作接口
     */
    public interface TransactionOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 获取事务状态信息
     */
    public static String getTransactionStatus() {
        if (isTransactionActive()) {
            Connection conn = connectionHolder.get();
            try {
                return String.format("Transaction Active - Connection: %d, AutoCommit: %s", 
                    conn.hashCode(), conn.getAutoCommit());
            } catch (SQLException e) {
                return "Transaction Active - Connection status unknown";
            }
        } else {
            return "No Active Transaction";
        }
    }
    
    /**
     * 强制清理事务状态（用于异常情况）
     */
    public static void forceCleanup() {
        try {
            if (isTransactionActive()) {
                rollbackTransaction();
            }
        } catch (Exception e) {
            System.err.println("Error during force cleanup: " + e.getMessage());
        } finally {
            connectionHolder.remove();
            transactionActive.remove();
        }
    }
}
