<?xml version="1.0" ?>

<project default="build" basedir=".">
	<tstamp>
	   <format property="TODAY_UK_M" pattern="yyyyMMdd" locale="en"/>
	   <format property="TODAY_UK" pattern="yyyyMMdd.HHmm" locale="en"/> 
	</tstamp>

	<!-- Name of project and version -->
	<property name="com.name" value="Aisino" /> 	<!-- 公司名 -->
	<property name="proj.name" value="A6" />		<!-- 项目名 -->
	<property name="JOB_NAME" value="SCM" />	<!-- 打包工程名 -->
<!-- 	<property name="JOB_NAME" value="SCM6.2" />			 -->
	<property name="module.version" value="7.1" />	<!-- 版本号 -->

	<!-- Global properties for this build -->
	<property name="lib.dir" location="D:\AutoDepforA6\dailybuild\A6\6.5" />				                                                <!-- lib目录 -->
	<property name="tomcat.lib.dir" location="D:\AutoDepforA6\dailybuild\apache-tomcat-6.0.10/lib"/>                                            <!-- Tomcat的lib目录 -->	
		
	<property name="build.dir" location="${basedir}\temp" />
	<property name="upload.dir" location="D:\Scripts\A6\" />		
	<property name="upload1.dir" location="\\*************\a6打包" />	
	<property name="upload7.1.dir" location="\\*************\a6提交目录" />	
	<property name="toA3.dir" location="D:\AutoDeployA6ForA3" />		
	<property name="my.build.classes.dir" location="${build.dir}/classes" />
	<property name="remote.java.dir" location="${basedir}\BuildResult\${TODAY_UK}"/>

	<!-- Classpath declaration -->
	<path id="project.classpath">
		<fileset dir="${remote.java.dir}">
			<include name="**/*.jar" />
		</fileset>		
		<fileset dir="${lib.dir}">
			<include name="**/*.jar" />
			<include name="**/*.zip" />
		</fileset>
		<fileset dir="${tomcat.lib.dir}">
			<include name="**/*.jar" />
		</fileset>	
	</path>

	<!-- Useful shortcuts -->
	<patternset id="meta.files">
		<include name="**/*.xml" />
		<include name="**/*.dtd" />
		<include name="**/*.js" />
		<include name="**/*.css" />
		<include name="**/*.gif" />
		<include name="**/*.sql" />
		<include name="**/*.jpg" />
		<include name="**/*.png" />
		<include name="**/*.xls" />
		<include name="**/*.cll" />
		<include name="**/*.vbs" />
		<include name="**/*.html" />
		<include name="**/*.cab" />
		<include name="**/*.jpeg" />		
	</patternset>
	
	<target name="copy">
	
     	<echo>复制构建结果</echo>
		
<!-- 		<copy todir="${toA3.dir}">
			<fileset dir="${remote.java.dir}">
			    <include name="Aisino-A6-A6COMMON-*.jar"/>
				<include name="Aisino-A6-BUSBASE-common-*.jar"/>
				<include name="Aisino-A6-UTIL-sysinit-*.jar"/>
			</fileset>			
		</copy>	 -->	
		<!--copy todir="${upload.dir}">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy-->		
		<copy todir="${upload7.1.dir}">
			<fileset dir="${remote.java.dir}">
			    <include name="Aisino-A6-YFK-7.1.jar"/>
			    <include name="Aisino-A6-UTIL-sysinit-7.1.jar"/>
			    <include name="Aisino-A6-UTIL-import-7.1.jar"/>
			    <include name="Aisino-A6-A6Reference-7.1.jar"/>
			    <include name="Aisino-A6-Finance-business-7.1.jar"/>
			    <include name="Aisino-A6-Finance-accountbook-7.1.jar"/>
			    <include name="Aisino-A6-BUSBASE-scm-7.1.jar"/>
			    <include name="Aisino-A6-A6COMMON-7.1.jar"/>
				<include name="Aisino-A6-EA-*.jar"/>			
				<include name="Aisino-A6-SA-*.jar"/>			
				<include name="Aisino-A6-PU-*.jar"/>			
				<include name="Aisino-A6-EA-*.zip"/>			
				<include name="Aisino-A6-SA-*.zip"/>			
				<include name="Aisino-A6-PU-*.zip"/>			
			</fileset>			
		</copy>		
		<copy todir="${upload1.dir}">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy>		
		<copy todir="D:\hudson\BuildResult\${TODAY_UK}">
			<fileset dir="${remote.java.dir}">
			</fileset>			
		</copy>	
<!-- 	    拷贝打包结果到变更集目录	 -->
		<copy todir="D:\HudsonChangeSet\${JOB_NAME}\BuildJarResult">
			<fileset dir="${remote.java.dir}" excludes="**/*.zip">			   
			</fileset>			
		</copy>		
		<echo>-----------------BUILD SUCCESSFUL-------------------------------</echo>		
	</target>
	

	<target name="build">
		<echo>-----------------prepare to build------------------------</echo>
		<antcall target="clear" />
		<antcall target="create" />
		<echo>-----------------building A6BUSINESSTEM-------------------------------</echo>
		<antcall target="jar">
		    <param name="module.name" value="A6BUSINESSTEM" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="A6BUSINESSTEM" />
		</antcall>	
		<echo>-----------------A6BUSINESSTEM build OK-------------------------------</echo>

		<echo>-----------------building A6COMMON-------------------------------</echo>
		<antcall target="jar">
		    <param name="module.name" value="A6COMMON" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="A6COMMON" />
		</antcall>	
		<echo>-----------------A6COMMON build OK-------------------------------</echo>

		<echo>-----------------building A6Reference-------------------------------</echo>
		<antcall target="jar">
		    <param name="module.name" value="A6Reference" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="A6Reference" />
		</antcall>	
		<echo>-----------------A6Reference build OK-------------------------------</echo>


		
		<echo>-----------------building UTIL-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="UTIL" />
			<param name="sub.name" value="import" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="UTIL" />
			<param name="sub.name" value="sysinit" />
		</antcall>	
		<antcall target="lib_copy">
		    <param name="module.name" value="UTIL" />
		</antcall>	
		<echo>-----------------UTiL build OK-------------------------------</echo>		

		<echo>-----------------building ARP-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="util" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="cancel" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="cashbill" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="close" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="exch" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="init" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="receivablebill" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ARP" />
			<param name="sub.name" value="statistic" />
		</antcall>

		<antcall target="lib_copy">
		    <param name="module.name" value="ARP" />
		</antcall>	
		<echo>-----------------ARP build OK-------------------------------</echo>

		<echo>-----------------building BI-------------------------------</echo>
		<antcall target="jar">
			<param name="module.name" value="BI" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="BI" />
		</antcall>
		<echo>-----------------BI build OK-------------------------------</echo>

		
		<echo>-----------------building BUSBASE-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="BUSBASE" />
			<param name="sub.name" value="common" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="BUSBASE" />
			<param name="sub.name" value="scm" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="BUSBASE" />
		</antcall>
		<echo>-----------------BUSBASE build OK-------------------------------</echo>

		<echo>-----------------building IA-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="IA" />
			<param name="sub.name" value="util" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="IA" />
			<param name="sub.name" value="account" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="IA" />
			<param name="sub.name" value="accountbill" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="IA" />
			<param name="sub.name" value="statistic" />
		</antcall>

		<antcall target="jar3">
			<param name="module.name" value="IA" />
			<param name="sub.name" value="initfinal" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="IA" />
		</antcall>	
		<echo>-----------------IA build OK-------------------------------</echo>
		
		<echo>-----------------building OM-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="OM" />
			<param name="sub.name" value="common" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="OM" />
			<param name="sub.name" value="invoice" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="OM" />
			<param name="sub.name" value="order" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="OM" />
			<param name="sub.name" value="price" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="OM" />
			<param name="sub.name" value="settle" />
		</antcall>
		<antcall target="jar4">
			<param name="module.name" value="OM" />
			<param name="sub.name" value="statistic" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="OM" />
			<param name="sub.name" value="stin" />
		</antcall>	
		<antcall target="lib_copy">
		    <param name="module.name" value="OM" />
		</antcall>	
		<echo>-----------------OM build OK------------------------------------</echo>
		
		<echo>-----------------building CZY-------------------------------</echo>
		<antcall target="jar2">
			<param name="module.name" value="CZY" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="CZY" />
		</antcall>
		<echo>-----------------CZY build OK-------------------------------</echo>
		
		<echo>-----------------building PU-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="util" />
		</antcall>	
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="require" />
		</antcall>	
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="account" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="carriage" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="init" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="invoice" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="order" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="plan" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="price" />
		</antcall>	
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="rebate" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="receive" />
		</antcall>	
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="settle" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="statistic" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="PU" />
			<param name="sub.name" value="stkin" />
		</antcall>	
		<antcall target="lib_copy">
		    <param name="module.name" value="PU" />
		</antcall>	
		<echo>-----------------PU build OK-------------------------------</echo>

		<echo>-----------------building QC-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="QC" />
			<param name="sub.name" value="defective" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="QC" />
			<param name="sub.name" value="macheck" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="QC" />
			<param name="sub.name" value="pucheck" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="QC" />
			<param name="sub.name" value="pureturn" />
		</antcall>		
		<antcall target="jar3">
			<param name="module.name" value="QC" />
			<param name="sub.name" value="statistic" />
		</antcall>		
		<antcall target="lib_copy">
		    <param name="module.name" value="QC" />
		</antcall>	
		<echo>-----------------QC build OK-------------------------------</echo>

		<echo>-----------------building SA-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="saprice" />
		</antcall>			
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="util" />
		</antcall>			
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="account" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="carriage" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="consignsettle" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="dispatch" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="gx" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="invoice" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="order" />
		</antcall>	
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="rebate" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="retail" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="sale_invGoldTax" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="statistic" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="stkout" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="znj" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="expense" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="SA" />
			<param name="sub.name" value="quote" />
		</antcall>			
		<antcall target="lib_copy">
		    <param name="module.name" value="SA" />
		</antcall>	

		<echo>-----------------SA build OK-------------------------------</echo>

		<echo>-----------------building ST-------------------------------</echo>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="util" />
		</antcall>	
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="account" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="adjustmentbill" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="assembly" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="barcode" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="init" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="shapechange" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="statistic" />
		</antcall>
		<antcall target="jar3">
			<param name="module.name" value="ST" />
			<param name="sub.name" value="storagebill" />
		</antcall>	
		<antcall target="lib_copy">
		    <param name="module.name" value="ST" />
		</antcall>	

		<echo>-----------------ST build OK-------------------------------</echo>
		<echo>-----------------building YFK-------------------------------</echo>
		<antcall target="jar2">
			<param name="module.name" value="YFK" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="YFK" />
		</antcall>
		<echo>-----------------YFK build OK-------------------------------</echo>


		<echo>-----------------building accountcloud-------------------------------</echo>
		<antcall target="jar">
			<param name="module.name" value="accountcloud" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="accountcloud" />
		</antcall>
		<echo>-----------------accountcloud build OK-------------------------------</echo>

		<echo>-----------------building HTTP_API-------------------------------</echo>
		<antcall target="jar">
			<param name="module.name" value="HTTP_API" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="HTTP_API" />
		</antcall>
		<echo>-----------------HTTP_API build OK-------------------------------</echo>


		<antcall target="webapp">
			<param name="module.name" value="webapp" />
		</antcall>
		<antcall target="lib_copy">
		    <param name="module.name" value="webapp" />
		</antcall>
		<echo>-----------------All Build Ok----------------------------</echo>
		<antcall target="copy" />

	</target>

	
	<target name="jar">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src\src</echo>
		<javac srcdir="${basedir}\${module.name}\src" executable="D:\Java\jdk1.7.0_79\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}" 
			classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\" >
				<!-- 如果源码目录下有需要排除的类文件 
				<exclude name="com/**" />
				-->
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\resource">
 				<patternset refid="meta.files" />
			</fileset>
		</jar>
		
	</target>

	<target name="jar2">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src</echo>
		<javac srcdir="${basedir}\${module.name}\src" executable="D:\Java\jdk1.7.0_79\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}" 
			classpathref="project.classpath">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\" >
				<!-- 如果源码目录下有需要排除的类文件 
				<exclude name="com/**" />
				-->
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\resource">
                      <patternset refid="meta.files" /> 
			</fileset>
		</jar>
		
		<echo>正在打包页面文件</echo>
		<zip destfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.zip" update="true" duplicate="preserve">
			<zipfileset dir="${basedir}\${module.name}\webapp" prefix="webapp"> 
			        <patternset refid="meta.files" />
			</zipfileset>
		</zip>
		
	</target>
	<target name="webapp">
	
		<echo>正在打包页面文件</echo>
		<zip destfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${module.version}.zip" update="true" duplicate="preserve">
			<zipfileset dir="${basedir}\${module.name}" prefix="webapp"> 
			        <patternset refid="meta.files" />
			</zipfileset>
		</zip>
	</target>		
	<target name="jar3">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
		
		<echo>编译类文件,来自: ${basedir}\${module.name}\src\src</echo>
		<javac srcdir="${basedir}\${module.name}\${sub.name}\src" executable="D:\Java\jdk1.7.0_79\bin\javac" listfiles="true" 
			encoding="utf8" nowarn="true" includeantruntime="on" debug="on" destdir="${my.build.classes.dir}\${module.name}\${sub.name}" 
            classpathref="project.classpath" excludes="com/aisino/a6/business/cancel/upgrade/**/*.java">
			<classpath refid="project.classpath" />
		</javac>	
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">
			<fileset dir="${my.build.classes.dir}\${module.name}\${sub.name}" >
				<!-- 如果源码目录下有需要排除的类文件 -->
				
			</fileset>
		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
                  <patternset refid="meta.files" />
			</fileset>
		</jar>
					
	</target>

	<target name="jar4">
	
		<mkdir dir="${my.build.classes.dir}\${module.name}\${sub.name}" /> 
	
        <echo>正在打包类文件</echo>

		<jar jarfile="${remote.java.dir}/${com.name}-${proj.name}-${module.name}-${sub.name}-${module.version}.jar">

		<!--复制资源文件,来自: ${basedir}\${proj.name}\code\${module.name}\resource-->			
			<fileset dir="${basedir}\${module.name}\${sub.name}\resource">
                 <patternset refid="meta.files" />
			</fileset>
		</jar>		
					
	</target>		

	<target name="create">
		<mkdir dir="${remote.java.dir}" />
		<mkdir dir="${upload.dir}" />
		<mkdir dir="D:\hudson\BuildResult\${TODAY_UK}" />
        <mkdir dir="D:\HudsonChangeSet\${JOB_NAME}\BuildJarResult" /> 	  		
	</target>

	<target name="clear">
		<delete dir="${build.dir}\temp" />		
	</target>
	
	<target name="lib_copy">
        <delete includeEmptyDirs="true" deleteonexit="true"> 
		<!--includeemptydirs：当使用fileset时，是否删除空目录，默认为false-->
		<!--deleteonexit：从Ant1.6.2起，如果删除文件失败，是否使用File的deleteOnExit方法。当设置为true时，JVM进程终止时会尝试删除文件。默认为false-->
            <fileset dir="${lib.dir}" includes="Aisino-A6-${module.name}-*.*"/>        </delete>
		<copy todir="${lib.dir}">
			<fileset dir="${remote.java.dir}">
			    <include name="Aisino-A6-${module.name}-*.jar"/>
				<include name="Aisino-A6-${module.name}-*.zip"/>			
			</fileset>			
		</copy>		
	</target>	
	

</project>
