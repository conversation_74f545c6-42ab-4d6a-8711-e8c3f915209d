<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID" version="2.4"
	xmlns="http://java.sun.com/xml/ns/j2ee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
	<display-name>aisino</display-name>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath*:spring/*.xml classpath:org/codehaus/xfire/spring/xfire.xml</param-value>
	</context-param>
	

   	<listener>
   	    <display-name>StartListener</display-name>
		<listener-class>com.aisino.platform.veng.servlet.ResourceLoadListener</listener-class>
	</listener>

	<!-- HTTP API Module Initializer -->
	<listener>
		<display-name>HttpApiInitializer</display-name>
		<listener-class>com.aisino.a6.http.init.HttpApiInitializer</listener-class>
	</listener>


	<servlet>
		<servlet-name>ptfile</servlet-name>
		<servlet-class>com.aisino.platform.veng.servlet.FileServlet</servlet-class>		
	</servlet>
	<servlet>
		<servlet-name>initForm</servlet-name>
		<servlet-class>com.aisino.platform.veng.servlet.InitFormServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>submitForm</servlet-name>
		<servlet-class>com.aisino.platform.veng.servlet.SubmitServlet</servlet-class>
	</servlet>	
	<servlet>
		<servlet-name>ptJs</servlet-name>
		<servlet-class>com.aisino.platform.veng.servlet.PtJsServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet>
		<servlet-name>ptCss</servlet-name>
		<servlet-class>com.aisino.platform.veng.servlet.PtCssServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>	
	
	<servlet>
		<servlet-name>xfire</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
	</servlet>	
	
	<servlet>
		<servlet-name>oa_weboffice</servlet-name>
		<servlet-class>com.aisino.a6.oa.common.util.WebOfficeServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>oa_weboffice</servlet-name>
		<url-pattern>/pt/weboffice</url-pattern>
	</servlet-mapping>

	<servlet>
		<servlet-name>wf_weboffice</servlet-name>		
		<servlet-class>com.aisino.platform.workflow.weboffice.WebOfficeServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>wf_weboffice</servlet-name>
		<url-pattern>/pt/wf_weboffice</url-pattern>
	</servlet-mapping>
	
	<servlet-mapping>
		<servlet-name>ptfile</servlet-name>
		<url-pattern>/ptfile/*</url-pattern>
	</servlet-mapping>
    <servlet-mapping>
		<servlet-name>xfire</servlet-name>
		<url-pattern>*.ws</url-pattern>
	</servlet-mapping>	
	<servlet-mapping>
		<servlet-name>initForm</servlet-name>
		<url-pattern>/pt/canvas</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>submitForm</servlet-name>
		<url-pattern>/pt/submit</url-pattern>
	</servlet-mapping>	
	<servlet-mapping>
		<servlet-name>ptJs</servlet-name>
		<url-pattern>/pt/pt.js</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ptCss</servlet-name>
		<url-pattern>*.ptcss</url-pattern>
	</servlet-mapping>


    <session-config>
        <session-timeout>120</session-timeout>
    </session-config>
	
	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
	</welcome-file-list>
	<error-page>
	    <error-code>404</error-code>
	    <location>/pt/error.html</location>	    
	</error-page>
	<error-page>
	    <error-code>500</error-code>
	    <location>/pt/error.html</location>	    
	</error-page>	
	
	<mime-mapping>  
	   <extension>doc</extension>     
	   <mime-type>application/msword</mime-type>     
	</mime-mapping>     
	<mime-mapping>     
	   <extension>xls</extension>     
	   <mime-type>application/vnd.ms-excel</mime-type>     
	</mime-mapping>     
	<mime-mapping>     
	   <extension>ppt</extension>     
	   <mime-type>application/vnd.ms-powerpoint</mime-type>     
	</mime-mapping>  
	
  <!-- optional? now in JPivot by default  -->
  <context-param>
    <param-name>contextFactory</param-name>
    <param-value>com.tonbeller.wcf.controller.RequestContextFactoryImpl</param-value>
  </context-param>

  <context-param>
    <param-name>connectString</param-name>
    <param-value>@mondrian.webapp.connectString@</param-value>
  </context-param>

  
  <!-- optional
  <context-param>
    <param-name>chartServlet</param-name>
    <param-value>/path/to/chartServlet</param-value>
  </context-param>
  -->

  <filter>
    <filter-name>JPivotController</filter-name>
    <filter-class>com.tonbeller.wcf.controller.RequestFilter</filter-class>
    <init-param>
      <param-name>errorJSP</param-name>
      <param-value>/error.jsp</param-value>
      <description>URI of error page</description>
    </init-param>
    <init-param>
      <param-name>busyJSP</param-name>
      <param-value>/busy.jsp</param-value>
      <description>This page is displayed if a the user clicks
        on a query before the previous query has finished</description>
    </init-param>
    <!--
    <init-param>
      <param-name>forceExtension</param-name>
      <param-value>.faces</param-value>
      <description>replace .jsp with .faces</description>
    </init-param>
    -->
  </filter>

  <filter-mapping>
    <filter-name>JPivotController</filter-name>
    <url-pattern>/olap.jsp</url-pattern>
  </filter-mapping>

  <filter>
    <filter-name>opensessioninview</filter-name>
    <filter-class>
      org.springframework.orm.hibernate3.support.OpenSessionInViewFilter
    </filter-class>
  </filter>

  <filter-mapping>
    <filter-name>opensessioninview</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  
  <listener>
    <listener-class>mondrian.web.taglib.Listener</listener-class>
  </listener>

  <!-- resources initializer -->
  <servlet>
    <servlet-name>MDXQueryServlet</servlet-name>
    <servlet-class>mondrian.web.servlet.MDXQueryServlet</servlet-class>
    <init-param>
      <param-name>connectString</param-name>
      <param-value>@mondrian.webapp.connectString@</param-value>
    </init-param>
  </servlet>

  <servlet>
    <servlet-name>MondrianXmlaServlet</servlet-name>
    <servlet-class>mondrian.xmla.impl.DefaultXmlaServlet</servlet-class>
    <!--
    <init-param>
      <param-name>DataSourcesConfig</param-name>
      <param-value>/datasources.xml</param-value>
    </init-param>
    -->
  </servlet>


  <!-- jfreechart provided servlet -->
  <servlet>
    <servlet-name>DisplayChart</servlet-name>
    <servlet-class>org.jfree.chart.servlet.DisplayChart</servlet-class>
  </servlet>

  <!-- jfreechart provided servlet -->
  <servlet>
    <servlet-name>GetChart</servlet-name>
    <display-name>GetChart</display-name>
    <description>Default configuration created for servlet.</description>
    <servlet-class>com.tonbeller.jpivot.chart.GetChart</servlet-class>
  </servlet>
  <servlet>
    <servlet-name>Print</servlet-name>
    <display-name>Print</display-name>
    <description>Default configuration created for servlet.</description>
    <servlet-class>com.tonbeller.jpivot.print.PrintServlet</servlet-class>
  </servlet>
  
  <servlet>
		<servlet-name>serviceForm</servlet-name>
		<servlet-class> com.aisino.platform.veng.servlet.ServiceServlet</servlet-class>
	</servlet>	
	<servlet-mapping>
		<servlet-name>serviceForm</servlet-name>
		<url-pattern>/pt/service</url-pattern>
	</servlet-mapping>
	<servlet>
		<servlet-name>download</servlet-name>
		<servlet-class>com.aisino.app.web.dotasklist.common.plugin.FileDownLoad</servlet-class>
	</servlet>
		
	<servlet-mapping>
		<servlet-name>download</servlet-name>
		<url-pattern>/download.do</url-pattern>
	</servlet-mapping>

  <servlet-mapping>
    <servlet-name>DisplayChart</servlet-name>
    <url-pattern>/DisplayChart</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>Print</servlet-name>
    <url-pattern>/Print</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>GetChart</servlet-name>
    <url-pattern>/GetChart</url-pattern>
  </servlet-mapping>

  <servlet-mapping>
    <servlet-name>MDXQueryServlet</servlet-name>
    <url-pattern>/mdxquery</url-pattern>
  </servlet-mapping>

  <servlet-mapping>
    <servlet-name>MondrianXmlaServlet</servlet-name>
    <url-pattern>/xmla</url-pattern>
  </servlet-mapping>
  
  <servlet>
	<servlet-name>dzservlet</servlet-name>
	<servlet-class>com.aisino.dz.yzw.trandata.servlet.DZservlet</servlet-class>
  </servlet>

   <servlet>
    <servlet-name>netSchoolServlet</servlet-name>
    <servlet-class>com.aisino.app.web.netschool.NetSchoolServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>netSchoolServlet</servlet-name>
    <url-pattern>/NetSchoolServlet</url-pattern>
  </servlet-mapping>
 <!--6.5start--> 
 <servlet>
    <servlet-name>expenseAccountBillUploadFile</servlet-name>
    <servlet-class>com.aisino.app.a6.web.dotasklist.bill.plugin.ExpenseAccountBillUploadFile</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>expenseAccountBillUploadFile</servlet-name>
    <url-pattern>/ExpenseAccountBillUploadFile</url-pattern>
  </servlet-mapping> 
   <servlet>
    <servlet-name>AppBaseServlet</servlet-name>
    <servlet-class>com.aisino.app.web.base.AppBaseServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>AppBaseServlet</servlet-name>
    <url-pattern>/AppBaseServlet</url-pattern>
  </servlet-mapping>

 <!--6.5end--> 
  
  <!--6.6sp03start-->
   <servlet>
    <servlet-name>AppendixUploadFile</servlet-name>
    <servlet-class>com.aisino.app.web.dotasklist.common.service.AppendixUploadFile</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>AppendixUploadFile</servlet-name>
    <url-pattern>/AppendixUploadFile</url-pattern>
  </servlet-mapping>   
  <!--6.6sp03end-->
  
  <servlet-mapping>
	<servlet-name>dzservlet</servlet-name>
	<url-pattern>/servlet/dzservlet</url-pattern>
  </servlet-mapping>

<!-- HTTP API Servlet -->
<servlet>
    <servlet-name>httpApi</servlet-name>
    <servlet-class>com.aisino.a6.http.api.HttpApiController</servlet-class>
    <load-on-startup>2</load-on-startup>
</servlet>
<servlet-mapping>
    <servlet-name>httpApi</servlet-name>
    <url-pattern>/openapi/api/*</url-pattern>
</servlet-mapping>

  <welcome-file-list>
    <welcome-file>index.html</welcome-file>
  </welcome-file-list>
<jsp-config>
  <taglib>
    <taglib-uri>http://www.tonbeller.com/wcf</taglib-uri>
    <taglib-location>/WEB-INF/wcf/wcf-tags.tld</taglib-location>
  </taglib>

  <taglib>
    <taglib-uri>http://www.tonbeller.com/jpivot</taglib-uri>
    <taglib-location>/WEB-INF/jpivot/jpivot-tags.tld</taglib-location>
  </taglib>
  </jsp-config>
</web-app>
